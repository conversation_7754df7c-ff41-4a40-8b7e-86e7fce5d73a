package com.xh.vdm.bi.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.enums.*;
import com.xh.vdm.bi.mapper.PntDeviceMapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bi.utils.DeviceNumUtils;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.SnowflakeIdWorker;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.FacilityTerminalRequest;
import com.xh.vdm.bi.vo.request.PntDeviceRequest;
import com.xh.vdm.bi.vo.request.TerminalIotRequest;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.FacilityNoBingResponse;
import com.xh.vdm.bi.vo.response.PntDeviceResponse;
import com.xh.vdm.biapi.entity.*;
import com.xh.vdm.interManager.feign.IMessageClient;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 北斗授时终端
 */
@Service
@Slf4j
public class PntDeviceServiceImpl extends ServiceImpl<PntDeviceMapper, BdmPntDevice> implements PntDeviceService {
	@Resource
	private PntDeviceMapper pntDeviceMapper;
	@Resource
	private IotCardService iotCardService;

	@Resource
	private CETokenUtil ceTokenUtil;
	@Resource
	private RedisTemplate redisTemplate;
	@Autowired
	private DeviceNumUtils deviceNumUtils;
	@Resource
	private IBdcTerminalService terminalService;
	@Resource
	private BdmDeviceCodeService deviceCodeService;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private BdmDeviceLedgerService deviceLedgerService;
	@Resource
	private IMessageClient messageClient;
	@Value("${current.schema}")
	String schema;
	@Resource
	private IBdmVirtualTargetService bdmVirtualTargetService;
	@Resource
	private IBdmAbstractDeviceService bdmAbstractDeviceService;
	@Resource
	private IBdmAbstractTargetService bdmAbstractTargetService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
	 * 分页查询
	 *
	 * @param request    筛选条件
	 * @param ceDataAuth
	 * @return 查询结果
	 */
	@Override
	public IPage<PntDeviceResponse> queryByPage(PntDeviceRequest request, DataAuthCE ceDataAuth) {
		Page page = new Page();
		page.setSize(request.getSize());
		page.setCurrent(request.getCurrent());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.pntDeviceMapper.queryAll(page, request, response.getAccount(), response.getOrgList(), schema);
	}

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public PntDeviceResponse queryById(Long id) {
		return this.pntDeviceMapper.queryById(id);
	}


	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmPntDevice insert(PntDeviceRequest request) {
		BdmPntDevice bdmPntDevice = new BdmPntDevice();
		QueryWrapper<BdmPntDevice> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("unique_id", request.getUniqueId());
		BdmPntDevice pnt = baseMapper.selectOne(queryWrapper);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		if (pnt != null) {
			request.setId(pnt.getId());
			if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			request.setCreateTime(new Date());
			request.setDeleted(0);
			//request.setDeviceNum(pnt.getDeviceNum());
			bdmPntDevice = this.init(request);
			//同步到待分配终端对象实体表
			QueryWrapper<BdmVirtualTarget> wrapper = new QueryWrapper<>();
			wrapper.eq("number", bdmPntDevice.getUniqueId());
			wrapper.last("LIMIT 1");
			BdmVirtualTarget latestRecord = bdmVirtualTargetService.getOne(wrapper);
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
//			BeanUtils.copyProperties(bdmPntDevice, virtualTarget, "id");
//			virtualTarget.setId(latestRecord.getId());
//			virtualTarget.setTargetType(0);
//			virtualTarget.setDeviceCode(bdmPntDevice.getDeviceNum());
//			virtualTarget.setNumber(bdmPntDevice.getUniqueId());
//			bdmVirtualTargetService.updateById(virtualTarget);
			BeanUtils.copyProperties(bdmPntDevice, virtualTarget);
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmPntDevice.getDeviceNum());
			virtualTarget.setNumber(bdmPntDevice.getUniqueId());
			if (latestRecord == null) {
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
				boolean saveRes = bdmVirtualTargetService.save(virtualTarget);
				if (saveRes) {
					log.info("bdmVirtualTargetService漏掉的 新增成功");
				} else {
					log.info("bdmVirtualTargetService漏掉的 新增失败:{}", virtualTarget);
				}
			} else {
				virtualTarget.setId(latestRecord.getId());
				boolean updateRes = bdmVirtualTargetService.updateById(virtualTarget);
				if (updateRes) {
					log.info("bdmVirtualTargetService 更新成功");
				} else {
					log.info("bdmVirtualTargetService 更新失败:{}", virtualTarget);
				}
			}
			QueryWrapper<BdmAbstractTarget> batWrapper = new QueryWrapper<>();
			batWrapper.eq("number", bdmPntDevice.getUniqueId());
			batWrapper.last("LIMIT 1");
			BdmAbstractTarget batLatestRecord = bdmAbstractTargetService.getOne(batWrapper);
			// 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(latestRecord.getId());
			bdmAbstractTarget.setNumber(bdmPntDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmPntDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmPntDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			if (batLatestRecord == null) {
				boolean saveRes = bdmAbstractTargetService.save(bdmAbstractTarget);
				if (saveRes) {
					log.info("bdmAbstractTargetService漏掉的 新增成功");
				} else {
					log.info("bdmAbstractTargetService漏掉的 新增失败:{}", bdmAbstractTarget);
				}
			} else {
				boolean updateRes = bdmAbstractTargetService.updateById(bdmAbstractTarget);
				if (updateRes) {
					log.info("bdmAbstractTargetService 更新成功");
				} else {
					log.info("bdmAbstractTargetService 更新失败:{}", bdmAbstractTarget);
				}
			}
			bdmPntDevice.setTargetId(virtualTarget.getId());
			baseMapper.update(bdmPntDevice);
			//同步到抽象监控对象实体表
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(bdmPntDevice, abstractDevice);
			abstractDevice.setIotProtocol(bdmPntDevice.getIotProtocol());
			abstractDevice.setTargetType(0);
			abstractDevice.setDeleted(0);
			bdmAbstractDeviceService.updateById(abstractDevice);
		} else {
			if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.PNT_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			request.setId(idWorker.nextId());
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			request.setCreateTime(new Date());
			bdmPntDevice = this.init(request);
			bdmPntDevice.setCreateAccount(AuthUtil.getUserAccount());
			//同步到待分配终端对象实体表
			//if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmPntDevice, virtualTarget);
			virtualTarget.setId(targetId.nextId());
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmPntDevice.getDeviceNum());
			virtualTarget.setNumber(bdmPntDevice.getUniqueId());
			virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
			bdmVirtualTargetService.save(virtualTarget);
			//TODO 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmPntDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmPntDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmPntDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			bdmAbstractTarget.setCreateAccount(AuthUtil.getUserAccount());
			bdmAbstractTargetService.save(bdmAbstractTarget);
			//}
			bdmPntDevice.setTargetId(virtualTarget.getId());
			this.pntDeviceMapper.insertPnt(bdmPntDevice);
			//同步到抽象监控对象实体表
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(bdmPntDevice, abstractDevice);
			abstractDevice.setIotProtocol(IotProtocolEnum.JT808.getCode());
			//abstractDevice.setTargetId(0L);
			abstractDevice.setTargetType(0);
			abstractDevice.setDeleted(0);
			abstractDevice.setCreateAccount(AuthUtil.getUserAccount());
			bdmAbstractDeviceService.saveDevice(abstractDevice);
		}

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			//更新存在且未绑定的物联网卡信息
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), bdmPntDevice.getId(), bdmPntDevice.getDeviceType());
		}

		if (bdmPntDevice.getSpecificity().equals(BdmTypeEnum.NEW.getValue())) {
			//跟新bdm_terminal
			terminalService.updateFormalByDeviceSeq(request.getUniqueId());
			//更新bdm_device_code
			deviceCodeService.updateActivated(request.getDeviceNum());
		}

		BdmPntDevice pntDevice = getBaseMapper().selectById(bdmPntDevice.getId());

		Map<String, String> map = new HashMap<>();
		String key = pntDevice.getDeviceType() + "-" + pntDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", pntDevice.getUniqueId());
		innerMap.put("deviceNum", pntDevice.getDeviceNum());
		innerMap.put("category", pntDevice.getCategory());
		innerMap.put("deviceType", pntDevice.getDeviceType());
		innerMap.put("deptId", pntDevice.getDeptId());
		innerMap.put("iotProtocol", pntDevice.getIotProtocol());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			Map<String, String> targetMap = new HashMap<>();
			String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + pntDevice.getId();
			Map<String, Object> targetInnerMap = new HashMap<>();
			targetInnerMap.put("targetName", pntDevice.getUniqueId());
			targetInnerMap.put("targetType", 0);
			targetInnerMap.put("deptId", pntDevice.getDeptId());
			try {
				map.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targetMap);
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setDeviceId(pntDevice.getId());
		deviceInfo.setDeviceType(pntDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(pntDevice.getUniqueId());
		deviceInfo.setDeviceModel(pntDevice.getModel());
		deviceInfo.setDeviceNum(pntDevice.getDeviceNum());
		deviceInfo.setDeptId(pntDevice.getDeptId());
		deviceInfo.setSpecificity(pntDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_pnt_device", JSON.toJSONString(new DeviceInfo()));
		} catch (Exception e) {
			log.error("授时终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_ADD, pntDevice.getDeviceType(), pntDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return pntDevice;
	}

	@Override
	public BdmPntDevice insertPntDevice(PntDeviceRequest request) {
		BdmPntDevice bdmPntDevice = new BdmPntDevice();
		QueryWrapper<BdmPntDevice> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("unique_id", request.getUniqueId());
		BdmPntDevice pnt = baseMapper.selectOne(queryWrapper);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		if (pnt != null) {
			request.setCreateTime(new Date());
			request.setDeleted(0);
			//request.setDeviceNum(pnt.getDeviceNum());
			bdmPntDevice = this.init(request);
			//同步到待分配终端对象实体表
			QueryWrapper<BdmVirtualTarget> wrapper = new QueryWrapper<>();
			wrapper.eq("number", bdmPntDevice.getUniqueId());
			wrapper.last("LIMIT 1");
			BdmVirtualTarget latestRecord = bdmVirtualTargetService.getOne(wrapper);
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmPntDevice, virtualTarget);
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmPntDevice.getDeviceNum());
			virtualTarget.setNumber(bdmPntDevice.getUniqueId());
			if (latestRecord == null) {
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
				boolean saveRes = bdmVirtualTargetService.save(virtualTarget);
				if (saveRes) {
					log.info("bdmVirtualTargetService漏掉的 新增成功");
				} else {
					log.info("bdmVirtualTargetService漏掉的 新增失败:{}", virtualTarget);
				}
			} else {
				virtualTarget.setId(latestRecord.getId());
				boolean updateRes = bdmVirtualTargetService.updateById(virtualTarget);
				if (updateRes) {
					log.info("bdmVirtualTargetService 更新成功");
				} else {
					log.info("bdmVirtualTargetService 更新失败:{}", virtualTarget);
				}
			}
			QueryWrapper<BdmAbstractTarget> batWrapper = new QueryWrapper<>();
			batWrapper.eq("number", bdmPntDevice.getUniqueId());
			batWrapper.last("LIMIT 1");
			BdmAbstractTarget batLatestRecord = bdmAbstractTargetService.getOne(batWrapper);
			// 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(latestRecord.getId());
			bdmAbstractTarget.setNumber(bdmPntDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmPntDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmPntDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			if (batLatestRecord == null) {
				boolean saveRes = bdmAbstractTargetService.save(bdmAbstractTarget);
				if (saveRes) {
					log.info("bdmAbstractTargetService漏掉的 新增成功");
				} else {
					log.info("bdmAbstractTargetService漏掉的 新增失败:{}", bdmAbstractTarget);
				}
			} else {
				boolean updateRes = bdmAbstractTargetService.updateById(bdmAbstractTarget);
				if (updateRes) {
					log.info("bdmAbstractTargetService 更新成功");
				} else {
					log.info("bdmAbstractTargetService 更新失败:{}", bdmAbstractTarget);
				}
			}
			bdmPntDevice.setTargetId(virtualTarget.getId());
			baseMapper.update(bdmPntDevice);
		} else {
			request.setCreateTime(new Date());
			bdmPntDevice = this.init(request);
			bdmPntDevice.setCreateAccount(AuthUtil.getUserAccount());
			//同步到待分配终端对象实体表
			//if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmPntDevice, virtualTarget);
			virtualTarget.setId(null);
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmPntDevice.getDeviceNum());
			virtualTarget.setNumber(bdmPntDevice.getUniqueId());
			virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
			bdmVirtualTargetService.save(virtualTarget);
			//TODO 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmPntDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmPntDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmPntDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			bdmAbstractTarget.setCreateAccount(AuthUtil.getUserAccount());
			bdmAbstractTargetService.save(bdmAbstractTarget);
			//}
			bdmPntDevice.setTargetId(virtualTarget.getId());
			bdmPntDevice.setId(null);
			this.pntDeviceMapper.insert(bdmPntDevice);
		}

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			//更新存在且未绑定的物联网卡信息
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), bdmPntDevice.getId(), bdmPntDevice.getDeviceType());
		}

		if (bdmPntDevice.getSpecificity().equals(BdmTypeEnum.NEW.getValue())) {
			//跟新bdm_terminal
			terminalService.updateFormalByDeviceSeq(request.getUniqueId());
			//更新bdm_device_code
			deviceCodeService.updateActivated(request.getDeviceNum());
		}

		BdmPntDevice pntDevice = getBaseMapper().selectById(bdmPntDevice.getId());

		Map<String, String> map = new HashMap<>();
		String key = pntDevice.getDeviceType() + "-" + pntDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", pntDevice.getUniqueId());
		innerMap.put("deviceNum", pntDevice.getDeviceNum());
		innerMap.put("category", pntDevice.getCategory());
		innerMap.put("deviceType", pntDevice.getDeviceType());
		innerMap.put("deptId", pntDevice.getDeptId());
		innerMap.put("iotProtocol", pntDevice.getIotProtocol());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			Map<String, String> targetMap = new HashMap<>();
			String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + pntDevice.getId();
			Map<String, Object> targetInnerMap = new HashMap<>();
			targetInnerMap.put("targetName", pntDevice.getUniqueId());
			targetInnerMap.put("targetType", 0);
			targetInnerMap.put("deptId", pntDevice.getDeptId());
			try {
				map.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targetMap);
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setDeviceId(pntDevice.getId());
		deviceInfo.setDeviceType(pntDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(pntDevice.getUniqueId());
		deviceInfo.setDeviceModel(pntDevice.getModel());
		deviceInfo.setDeviceNum(pntDevice.getDeviceNum());
		deviceInfo.setDeptId(pntDevice.getDeptId());
		deviceInfo.setSpecificity(pntDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_pnt_device", JSON.toJSONString(new DeviceInfo()));
		} catch (Exception e) {
			log.error("授时终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_ADD, pntDevice.getDeviceType(), pntDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return pntDevice;
	}

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmPntDevice update(PntDeviceRequest request) {

		BdmPntDevice bdmPntDevice = this.init(request);
		this.pntDeviceMapper.update(bdmPntDevice);

		iotCardService.updateDeviceIdById(request.getId(), request.getDeviceType(), request.getNumbers());

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), request.getId(), request.getDeviceType());
		}

		BdmPntDevice pntDevice = getBaseMapper().selectById(bdmPntDevice.getId());

		// 未绑定的非新设备更新抽象终端表
		if (pntDevice.getSpecificity() != BdmTypeEnum.NEW.getValue()) {
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(pntDevice, abstractDevice);
			bdmAbstractDeviceService.updateById(abstractDevice);
		}

		String key = pntDevice.getDeviceType() + "-" + bdmPntDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", pntDevice.getUniqueId());
		innerMap.put("deviceNum", pntDevice.getDeviceNum());
		innerMap.put("category", pntDevice.getCategory());
		innerMap.put("deviceType", pntDevice.getDeviceType());
		innerMap.put("deptId", pntDevice.getDeptId());
		innerMap.put("iotProtocol", pntDevice.getIotProtocol());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setDeviceId(pntDevice.getId());
		deviceInfo.setDeviceType(pntDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(pntDevice.getUniqueId());
		deviceInfo.setDeviceModel(pntDevice.getModel());
		deviceInfo.setDeviceNum(pntDevice.getDeviceNum());
		deviceInfo.setDeptId(pntDevice.getDeptId());
		deviceInfo.setSpecificity(pntDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_pnt_device", JSON.toJSONString(new DeviceInfo()));
		} catch (Exception e) {
			log.error("授时终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_UPDATE, pntDevice.getDeviceType(), pntDevice);
		} catch (Exception e) {
			log.error("穿戴式终端信息更新消息发送到kafka失败", e);
		}
		return pntDevice;
	}

	@Override
	public BdmPntDevice updatePntDevice(PntDeviceRequest request) {
		BdmPntDevice bdmPntDevice = this.init(request);
		this.pntDeviceMapper.update(bdmPntDevice);

		iotCardService.updateDeviceIdById(request.getId(), request.getDeviceType(), request.getNumbers());

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), request.getId(), request.getDeviceType());
		}

		BdmPntDevice pntDevice = getBaseMapper().selectById(bdmPntDevice.getId());

		String key = pntDevice.getDeviceType() + "-" + bdmPntDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", pntDevice.getUniqueId());
		innerMap.put("deviceNum", pntDevice.getDeviceNum());
		innerMap.put("category", pntDevice.getCategory());
		innerMap.put("deviceType", pntDevice.getDeviceType());
		innerMap.put("deptId", pntDevice.getDeptId());
		innerMap.put("iotProtocol", pntDevice.getIotProtocol());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setDeviceId(pntDevice.getId());
		deviceInfo.setDeviceType(pntDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(pntDevice.getUniqueId());
		deviceInfo.setDeviceModel(pntDevice.getModel());
		deviceInfo.setDeviceNum(pntDevice.getDeviceNum());
		deviceInfo.setDeptId(pntDevice.getDeptId());
		deviceInfo.setSpecificity(pntDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_pnt_device", JSON.toJSONString(new DeviceInfo()));
		} catch (Exception e) {
			log.error("授时终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_UPDATE, pntDevice.getDeviceType(), pntDevice);
		} catch (Exception e) {
			log.error("穿戴式终端信息更新消息发送到kafka失败", e);
		}
		return pntDevice;
	}

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 是否成功
	 */
	@Override
	public boolean deleteById(Long id) {
		return this.pntDeviceMapper.deleteById(id) > 0;
	}

	/**
	 * 通过主键删除数据
	 *
	 * @param ids 主键
	 * @return 是否成功
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIds(Long[] ids) {

		QueryWrapper<BdmPntDevice> wrapper = new QueryWrapper<>();
		wrapper.in("id", ids);
		List<BdmPntDevice> list = this.pntDeviceMapper.selectList(wrapper);

		List<String> deviceNumList = new ArrayList<>();
		List<String> uniqueIdList = new ArrayList<>();

		if (!list.isEmpty()) {
			// 查询 deviceNum 列表
			deviceNumList = list.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
				.map(BdmPntDevice::getDeviceNum)
				.collect(Collectors.toList());

			// 查询 uniqueId 列表
			uniqueIdList = list.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
				.map(BdmPntDevice::getUniqueId)
				.collect(Collectors.toList());
		}

		boolean result = this.pntDeviceMapper.deleteByIds(ids) > 0;
		if (result) {
			for (Long id : ids) {
				String key = BaseInfoConstants.PNT_DEVICE_TYPE + "-" + id;
				redisTemplate.delete(BaseInfoConstants.BASEINFO_DEVICE + ":" + key);
			}
			this.iotCardService.deleteByDeviceIds(ids, DeviceTypeEnum.PNT.getSymbol());
			if (!uniqueIdList.isEmpty()) {
				//跟新bdm_terminal
				terminalService.updateFormalByDeviceSeqs(uniqueIdList);
			}
			if (!deviceNumList.isEmpty()) {
				//更新bdm_device_code
				deviceCodeService.updateActivatedByDeviceNum(deviceNumList);
			}

			DeviceInfo deviceInfo = new DeviceInfo();
			Long lastId = ids[ids.length - 1];
			deviceInfo.setDeviceId(lastId);
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_pnt_device", JSON.toJSONString(new DeviceInfo()));
			} catch (Exception e) {
				log.error("授时终端信息更新消息发送到kafka失败", e);
			}
			//messageClient
			try {
				List<Object> terminals = new ArrayList<>(list);
				messageClient.terminalBatch(CommonConstant.OPER_TYPE_DELETE, BaseInfoConstants.PNT_DEVICE_TYPE, terminals);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}

			//同步更新抽象监控对象实体表
			bdmAbstractDeviceService.deleteByIds(ids);
			// 同步更新待分配终端对象实体表
			if (!uniqueIdList.isEmpty()) {
				bdmVirtualTargetService.updateByUniqueId(uniqueIdList);

				QueryWrapper<BdmVirtualTarget> virtualTargetQueryWrapper = new QueryWrapper<>();
				if (!uniqueIdList.isEmpty()) {
					MybatisPlusWrapperUitls.longListIn(wrapper, "unique_id", uniqueIdList);
				}
				virtualTargetQueryWrapper.select("id");
				List<Long> idList = bdmVirtualTargetService.getBaseMapper()
					.selectList(virtualTargetQueryWrapper)
					.stream()
					.map(BdmVirtualTarget::getId)
					.collect(Collectors.toList());
				// 同步更新抽象监控对象实体表
				if (!idList.isEmpty()) {
					bdmAbstractTargetService.deleteByIds(idList.toArray(new Long[0]));
					for (Long id : idList) {
						String targetKey = BaseInfoConstants.VIRTUAL_TARGET_TYPE + "-" + id;
						redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET + ":" + targetKey);
					}
				}
			}

		}
		return result;
	}

	@Override
	public IPage<FacilityNoBingResponse> selectNoBind(DeviceNoBindRequest deviceNoBindRequest, String account, String deptIds) {
		Page page = new Page();
		page.setSize(deviceNoBindRequest.getSize());
		page.setCurrent(deviceNoBindRequest.getCurrent());
		return this.pntDeviceMapper.selectNoBind(page, deviceNoBindRequest, account, deptIds);
	}

	@Override
	public List<FacilityNoBingResponse> selectBindByFacilityId(Long id, Integer targetType, DeviceNoBindRequest request) {
		return this.pntDeviceMapper.selectBindByFacilityId(id, targetType, request);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<PntDeviceRequest> importExcel(List<PntDeviceRequest> list, Long userId) {
		List<PntDeviceRequest> duplicateRequests = getDuplicateRequests(list);
		// 假设requests和duplicateRequests已经被正确初始化
		List<PntDeviceRequest> requests = new ArrayList<>(list);
		// 移除duplicateRequests中的所有元素
		requests.removeIf(request -> duplicateRequests.contains(request));
		if (!requests.isEmpty()) {
			List<PntDeviceRequest> filteredList = processRequests(requests, userId, duplicateRequests);
			if (!filteredList.isEmpty()) {
				List<Long> arrayList = updateDatabaseAndCache(filteredList);
				sendNotifications(filteredList, arrayList);
			}
		}
		return duplicateRequests;
	}

	/**
	 * 重复数据
	 *
	 * @param list
	 * @return
	 */
	public List<PntDeviceRequest> getDuplicateRequests(List<PntDeviceRequest> list) {
		// 映射并处理可能的null值
		List<String> numberList = list.stream()
			.map(PntDeviceRequest::getNumbers)
			.map(Optional::ofNullable)
			.map(optional -> optional.orElse(null))
			.distinct()
			.collect(Collectors.toList());

		// 找出重复的物联网卡号
		List<String> duplicates = numberList.stream()
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
			.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		// 找出重复的序列号
		List<String> uniqueIdList = list.stream()
			.map(PntDeviceRequest::getUniqueId)
			.collect(Collectors.toList());

		List<String> duplicateUniqueIds = uniqueIdList.stream()
			.filter(id -> Collections.frequency(uniqueIdList, id) > 1)
			.distinct()
			.collect(Collectors.toList());

		return list.stream()
			.filter(pntDeviceRequest -> {
				String numbers = pntDeviceRequest.getNumbers();
				boolean isDuplicateNumbers = numbers != null && duplicates.contains(numbers);
				boolean isDuplicateUniqueId = duplicateUniqueIds.contains(pntDeviceRequest.getUniqueId());
				return isDuplicateNumbers || isDuplicateUniqueId;
			})
			.peek(pntDeviceRequest -> {
				if (pntDeviceRequest.getNumbers() != null && duplicates.contains(pntDeviceRequest.getNumbers())) {
					pntDeviceRequest.setMsg("导入的数据物联网卡号重复");
				}
				if (duplicateUniqueIds.contains(pntDeviceRequest.getUniqueId())) {
					pntDeviceRequest.setMsg("导入的数据序列号重复");
				}
			})
			.collect(Collectors.toList());
	}

	/**
	 * 数据处理
	 */
	private List<PntDeviceRequest> processRequests(List<PntDeviceRequest> requests, Long userId, List<PntDeviceRequest> duplicateRequests) {
		// 按部门ID分组
		Map<Long, List<PntDeviceRequest>> groupedByDept = requests.stream()
			.collect(Collectors.groupingBy(PntDeviceRequest::getDeptId));

		List<PntDeviceRequest> filterList = new ArrayList<>();

		for (Map.Entry<Long, List<PntDeviceRequest>> entry : groupedByDept.entrySet()) {
			// TODO 终端赋码号不存在！和 序列号已存在！可连表查进行判断 检查输入的赋码号的合法性
			Map<String, BdmDeviceLedger> deviceLedgerMap = getDeviceLedgers(entry.getKey())
				.stream()
				.collect(Collectors.toMap(BdmDeviceLedger::getDeviceNum, ledger -> ledger));

			List<PntDeviceRequest> requestsForDept = entry.getValue();

			// 过滤出该部门下设备编号存在于deviceLedgers中的请求
			List<PntDeviceRequest> validRequestsForDept = requestsForDept.stream()
				.filter(request -> {
					if (!BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
						return true;
					} else {
						return isValidRequest(request, deviceLedgerMap);
					}
				})
				.collect(Collectors.toList());

			filterList.addAll(validRequestsForDept);

			//检查输入的赋码号的合法性
			requestsForDept.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()) && !deviceLedgerMap.containsKey(request.getDeviceNum()))
				.peek(request -> request.setMsg("终端赋码号不存在！"))
				.forEach(duplicateRequests::add);
		}

		if (!filterList.isEmpty()) {
			List<String> uniqueIdList = filterList.stream()
				.filter(Objects::nonNull)
				.map(PntDeviceRequest::getUniqueId)
				.collect(Collectors.toList());

			QueryWrapper<BdmPntDevice> wrapper = new QueryWrapper<>();
			wrapper.eq("deleted", 0);
			if (!uniqueIdList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(wrapper, "unique_id", uniqueIdList);
			}
			wrapper.select("unique_id");
			//序列号判重
			List<String> uniqueIds = baseMapper.selectList(wrapper)
				.stream()
				.map(BdmPntDevice::getUniqueId)
				.collect(Collectors.toList());

			List<PntDeviceRequest> resultList = filterList.stream()
				.filter(pntDeviceRequest ->
					pntDeviceRequest.getUniqueId() != null && !uniqueIds.contains(pntDeviceRequest.getUniqueId()))
				.collect(Collectors.toList());

			filterList.stream()
				.filter(request -> !resultList.contains(request))
				.peek(request -> request.setMsg("序列号已存在！"))
				.forEach(duplicateRequests::add);
			filterList = resultList;

			if (!filterList.isEmpty()) {
				List<String> numberedList = resultList.stream()
					.filter(Objects::nonNull)
					.map(PntDeviceRequest::getNumbers)
					.collect(Collectors.toList());

				List<String> numberList = new ArrayList<>();

				if (!numberedList.isEmpty()) {


					log.info("[importExcel]终端信息导入，将要判断是否是底座登录");
					DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
					boolean isCE = ceTokenUtil.isCELogin();
					if(isCE){
						log.info("[importExcel]终端信息导入，国能底座登录");
						//如果是国能底座，则查询数据权限范围内的物联网卡信息
						String account = ceDataAuth.getAccount();
						String deptArrayStr = ceDataAuth.getOrgListStr();
						numberList = iotCardService.queryIotCE(numberedList, account, deptArrayStr);
						log.info("[importExcel]终端信息导入，获取数据权限范围内的物联网卡数量为：{}", numberList==null?0:numberList.size());
					}else{
						userId = AuthUtil.isAdministrator() ? null : userId;
						// 物联网卡验证
						numberList = iotCardService.queryIot(numberedList, userId);
					}
				}

				final List<String> finalNumberList = numberList;

				List<PntDeviceRequest> filteredList = resultList.stream()
					.filter(pntDeviceRequest ->
						pntDeviceRequest.getNumbers() == null || finalNumberList.contains(pntDeviceRequest.getNumbers()))
					.collect(Collectors.toList());

				// 将除了filteredList的数据加到duplicateRequests
				resultList.stream()
					.filter(request -> !filteredList.contains(request))
					.peek(request -> request.setMsg("该物联网卡号不存在或已绑定！"))
					.forEach(duplicateRequests::add);

				filterList = filteredList;
			}
		}

		return filterList;
	}

	/**
	 * 查询出库到本单位数据
	 */
	private List<BdmDeviceLedger> getDeviceLedgers(Long deptId) {
		return deviceLedgerService.getBaseMapper()
			.selectList(new QueryWrapper<BdmDeviceLedger>()
				.eq("device_type", BaseInfoConstants.PNT_DEVICE_TYPE)
				.eq("user_dept_id", deptId));
	}

	/**
	 * 新设备给设置正确的赋码信息
	 */
	private boolean isValidRequest(PntDeviceRequest request, Map<String, BdmDeviceLedger> targetMap) {
		BdmDeviceLedger deviceLedger = targetMap.get(request.getDeviceNum());
		if (deviceLedger != null) {
			request.setModel(deviceLedger.getModel());
			request.setImei(deviceLedger.getImei());
			request.setVendor(deviceLedger.getVendor());
			request.setUniqueId(deviceLedger.getUniqueId());
			request.setBdChipSn(deviceLedger.getBdChipSn());
			request.setDeviceType(deviceLedger.getDeviceType());
			request.setCategory(deviceLedger.getCategory());
			return true;
		}
		return false;
	}

	/**
	 * 更新数据库和进行数据redis缓存
	 */
	private List<Long> updateDatabaseAndCache(List<PntDeviceRequest> filteredList) {

		SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.PNT_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		Map<String, BdmPntDevice> pntMap = baseMapper.selectList(new QueryWrapper<BdmPntDevice>().eq("deleted", 1))
			.stream()
			.collect(Collectors.toMap(BdmPntDevice::getUniqueId, PntDevice -> PntDevice));
		//用于判断恢复被删除的设备时，判断是执行新增还是恢复操作
		Map<String, BdmVirtualTarget> allVirtualTargetMap = bdmVirtualTargetService.list()
			.stream()
			.collect(Collectors.toMap(BdmVirtualTarget::getNumber, VirtualTarget -> VirtualTarget, (existing, replacement) -> existing));
		Map<String, BdmAbstractTarget> allBatMap = bdmAbstractTargetService.list()
			.stream()
			.collect(Collectors.toMap(BdmAbstractTarget::getNumber, bat -> bat, (existing, replacement) -> existing));
		// 创建一个 Map 来存储所有的键值对
		List<TerminalIotRequest> terminalIotRequests = new ArrayList<>();
		Map<String, String> map = new HashMap<>();
		Map<String, String> targrtMap = new HashMap<>();
		List<Long> arrayList = new ArrayList<>();
		List<BdmVirtualTarget> virtualTargets = new ArrayList<>();
		List<BdmAbstractDevice> abstractDevices = new ArrayList<>();
		List<BdmAbstractTarget> abstractTargets = new ArrayList<>();
		// 在处理filteredList之前，收集所有新设备的序列号
		Set<String> newDeviceUniqueIds = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(PntDeviceRequest::getUniqueId)
			.collect(Collectors.toSet());
		Map<String, BdmVirtualTarget> virtualTargetMap = new HashMap<>();
		// 一次性查询所有匹配的BdmVirtualTarget记录
		if (!newDeviceUniqueIds.isEmpty()) {
			List<BdmVirtualTarget> allVirtualTargets = bdmVirtualTargetService.list(
				new QueryWrapper<BdmVirtualTarget>().in("number", newDeviceUniqueIds).select("id").select("number")
			);
			// 将查询结果存储在一个Map中，以便后续快速查找
			virtualTargetMap = allVirtualTargets.stream().collect(Collectors.toMap(BdmVirtualTarget::getNumber, Function.identity()));
		}
		for (PntDeviceRequest request : filteredList) {
			BdmPntDevice device = pntMap.get(request.getUniqueId());
			// device为空新增，不为空修改
			long deviceId = (device == null) ? idWorker.nextId() : device.getId();
			request.setId(deviceId);
			request.setDeleted(0);
			//设备赋码
			if (device == null) {
				if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
					R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
					if (r1.isSuccess()) {
						request.setDeviceNum(r1.getData());
					}
				}
				if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
					R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
					if (r1.isSuccess()) {
						request.setDeviceNum(r1.getData());
					}
				}
			} else {
				request.setDeviceNum(device.getDeviceNum());
			}
			//新设备的定位模式单独处理
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			request.setCreateTime(new Date());
			BdmPntDevice bdmPntDevice = this.init(request);
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			if (device == null) {
				//同步到待分配终端对象实体表
				BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
				BeanUtils.copyProperties(bdmPntDevice, virtualTarget, "id");
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setTargetType(0);
				virtualTarget.setDeviceCode(bdmPntDevice.getDeviceNum());
				virtualTarget.setNumber(bdmPntDevice.getUniqueId());
				virtualTargets.add(virtualTarget);

				// TODO 同步到抽象监控对象实体表 abstractTargets
				bdmAbstractTarget.setId(virtualTarget.getId());
				bdmAbstractTarget.setNumber(bdmPntDevice.getUniqueId());
				bdmAbstractTarget.setName(bdmPntDevice.getUniqueId());
				bdmAbstractTarget.setTargetType(0);
				bdmAbstractTarget.setDeptId(bdmPntDevice.getDeptId());
				bdmAbstractTarget.setCategory(0);
				bdmAbstractTarget.setDeleted(0);
				abstractTargets.add(bdmAbstractTarget);
				bdmPntDevice.setTargetId(virtualTarget.getId());
				this.pntDeviceMapper.insertPnt(bdmPntDevice);
				//同步到抽象监控对象实体表
				BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
				BeanUtils.copyProperties(bdmPntDevice, abstractDevice);
				//abstractDevice.setTargetId(0L);
				abstractDevice.setTargetType(0);
				abstractDevice.setDeleted(0);
				abstractDevices.add(abstractDevice);
			} else {
				//同步到待分配终端对象实体表
				BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
				BeanUtils.copyProperties(bdmPntDevice, virtualTarget, "id");
				virtualTarget.setTargetType(0);
				virtualTarget.setDeviceCode(bdmPntDevice.getDeviceNum());
				virtualTarget.setNumber(bdmPntDevice.getUniqueId());
				if (allVirtualTargetMap.containsKey(device.getUniqueId())) {
					virtualTarget.setId(virtualTargetMap.get(bdmPntDevice.getUniqueId()).getId());
					bdmVirtualTargetService.updateById(virtualTarget);
				} else {
					virtualTarget.setId(targetId.nextId());
					virtualTargets.add(virtualTarget);
				}
				// TODO 更新到抽象监控对象实体表
				bdmAbstractTarget.setNumber(bdmPntDevice.getUniqueId());
				bdmAbstractTarget.setName(bdmPntDevice.getUniqueId());
				bdmAbstractTarget.setTargetType(0);
				bdmAbstractTarget.setDeptId(bdmPntDevice.getDeptId());
				bdmAbstractTarget.setCategory(0);
				bdmAbstractTarget.setDeleted(0);
				bdmAbstractTarget.setId(virtualTarget.getId());
				if (allBatMap.containsKey(device.getUniqueId())) {
					bdmAbstractTargetService.updateById(bdmAbstractTarget);
				} else {
					abstractTargets.add(bdmAbstractTarget);
				}

				bdmPntDevice.setTargetId(virtualTarget.getId());
				baseMapper.updateById(bdmPntDevice);
				//同步到抽象监控对象实体表
				BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
				BeanUtils.copyProperties(bdmPntDevice, abstractDevice);
				abstractDevice.setTargetType(0);
				abstractDevice.setDeleted(0);
				bdmAbstractDeviceService.updateById(abstractDevice);
			}
			request.setId(bdmPntDevice.getId());
			arrayList.add(bdmPntDevice.getId());
			if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
				TerminalIotRequest terminalIotRequest = new TerminalIotRequest();
				terminalIotRequest.setId(bdmPntDevice.getId());
				terminalIotRequest.setNumber(request.getNumbers());
				terminalIotRequest.setDeviceType(BaseInfoConstants.PNT_DEVICE_TYPE);
				terminalIotRequests.add(terminalIotRequest);
			}
			String key = bdmPntDevice.getDeviceType() + "-" + bdmPntDevice.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("uniqueId", bdmPntDevice.getUniqueId());
			innerMap.put("deviceNum", bdmPntDevice.getDeviceNum());
			innerMap.put("category", bdmPntDevice.getCategory());
			innerMap.put("deviceType", bdmPntDevice.getDeviceType());
			innerMap.put("deptId", bdmPntDevice.getDeptId());
			innerMap.put("iotProtocol", bdmPntDevice.getIotProtocol());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			// 待分配终端对象 缓存到 baseinfo_target
			if (bdmPntDevice.getSpecificity().equals(BdmTypeEnum.NEW.getValue())) {
				String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + bdmAbstractTarget.getId();
				Map<String, Object> targetInnerMap = new HashMap<>();
				targetInnerMap.put("targetName", bdmAbstractTarget.getNumber());
				targetInnerMap.put("targetType", bdmAbstractTarget.getTargetType());
				targetInnerMap.put("deptId", bdmAbstractTarget.getDeptId());
				try {
					targrtMap.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
		}
		if (!virtualTargets.isEmpty()) {
			MapperUtils.splitListByCapacity(virtualTargets, MapperUtils.DEFAULT_CAPACITY)
				.forEach(virtualTargetList -> this.bdmVirtualTargetService.insertBatch(virtualTargetList));
		}
		if (!abstractDevices.isEmpty()) {
			MapperUtils.splitListByCapacity(abstractDevices, MapperUtils.DEFAULT_CAPACITY)
				.forEach(abstractDeviceList -> this.bdmAbstractDeviceService.insertBatch(abstractDeviceList));
		}
		// 新设备同步到抽象监控对象实体表
		if (!abstractTargets.isEmpty()) {
			MapperUtils.splitListByCapacity(abstractTargets, MapperUtils.DEFAULT_CAPACITY)
				.forEach(abstractTargetList -> this.bdmAbstractTargetService.insertBatch(abstractTargetList));
		}

		if (!map.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
		}
		if (!targrtMap.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targrtMap);
		}
		if (!terminalIotRequests.isEmpty()) {
			this.iotCardService.updateDeviceId(terminalIotRequests);
		}

		// 查询 deviceNum 列表
		List<String> deviceNumList = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(PntDeviceRequest::getDeviceNum)
			.collect(Collectors.toList());

		// 查询 uniqueId 列表
		List<String> uniqueIdList = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(PntDeviceRequest::getUniqueId)
			.collect(Collectors.toList());

		if (!uniqueIdList.isEmpty()) {
			//跟新bdm_terminal
			terminalService.updateFormalByDeviceSeqs(uniqueIdList);
		}
		if (!deviceNumList.isEmpty()) {
			//更新bdm_device_code
			deviceCodeService.updateActivatedByDeviceNum(deviceNumList);
		}
		return arrayList;
	}

	/**
	 * 发送消息
	 */
	private void sendNotifications(List<PntDeviceRequest> filteredList, List<Long> arrayList) {
		PntDeviceRequest request = filteredList.get(filteredList.size() - 1);
		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setDeviceId(request.getId());
		deviceInfo.setDeviceType(request.getDeviceType());
		deviceInfo.setDeviceModel(request.getModel());
		deviceInfo.setDeviceNum(request.getDeviceNum());
		deviceInfo.setDeviceUniqueId(request.getUniqueId());
		deviceInfo.setDeptId(request.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_pnt_device", JSON.toJSONString(new DeviceInfo()));
		} catch (Exception e) {
			log.error("授时终端信息更新消息发送到kafka失败", e);
		}
		QueryWrapper<BdmPntDevice> wrapper = new QueryWrapper<>();
		if (!arrayList.isEmpty()) {
			MybatisPlusWrapperUitls.longListIn(wrapper, "id", arrayList);
			List<BdmPntDevice> pntDevices = baseMapper.selectList(wrapper);
			//messageClient
			List<Object> terminals = new ArrayList<>(pntDevices);
			try {
				messageClient.terminalBatch(CommonConstant.OPER_TYPE_ADD, BaseInfoConstants.PNT_DEVICE_TYPE, terminals);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateByDeviceId(Long id, Integer targetType) {
		this.pntDeviceMapper.updateByDeviceId(id, targetType);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateBatchByTerminalId(List<FacilityTerminalRequest> pnts, Long id, Integer targetType, Long deptId) {
		Map<String, String> map = new HashMap<>();
		for (FacilityTerminalRequest request : pnts) {
			this.pntDeviceMapper.updateBatchByTerminalId(request, id, targetType, deptId);

			String key = request.getDeviceType() + "-" + request.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("uniqueId", request.getUniqueId());
			innerMap.put("deviceNum", request.getDeviceNum());
			innerMap.put("category", request.getCategory());
			innerMap.put("deviceType", request.getDeviceType());
			innerMap.put("deptId", request.getDeptId());
			innerMap.put("iotProtocol", request.getIotProtocol());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				log.error(e.getMessage());
			}
		}
		if (!map.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteByTargetIds(Long[] ids) {
		this.pntDeviceMapper.deleteByTargetIds(ids);
	}

	@Override
	public long countByUserRole(DataAuthCE ceDataAuth) {
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return baseMapper.countByUserRole(response.getAccount(), response.getOrgList());
	}

	@Override
	public void updateDept(Long id, Integer targetType, Long deptId) {
		List<BdmPntDevice> pntDevices = baseMapper.selectList(
			new QueryWrapper<BdmPntDevice>()
				.eq("target_id", id)
				.eq("target_type", targetType)
				.eq("deleted", 0)
				.select("id,unique_id,device_type,dept_id,category,device_num,iot_protocol")
		);
		if (!pntDevices.isEmpty()) {
			baseMapper.updateDept(id, targetType, deptId);

			Map<String, String> map = new HashMap<>();
			for (BdmPntDevice pntDevice : pntDevices) {
				String key = pntDevice.getDeviceType() + "-" + pntDevice.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("uniqueId", pntDevice.getUniqueId());
				innerMap.put("deviceNum", pntDevice.getDeviceNum());
				innerMap.put("category", pntDevice.getCategory());
				innerMap.put("deviceType", pntDevice.getDeviceType());
				innerMap.put("deptId", deptId);
				innerMap.put("iotProtocol", pntDevice.getIotProtocol());
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
			if (!map.isEmpty()) {
				redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
			}
		}
	}

	/**
	 * 对象字段初始化
	 *
	 * @param request
	 * @return
	 */
	public BdmPntDevice init(PntDeviceRequest request) {
		BdmPntDevice pntDevice = new BdmPntDevice();
		pntDevice.setId(request.getId());
		pntDevice.setUniqueId(request.getUniqueId() != null ? request.getUniqueId() : "");
		pntDevice.setImei(request.getImei() != null ? request.getImei() : "");
		pntDevice.setModel(request.getModel() != null ? request.getModel() : "");
		pntDevice.setVendor(request.getVendor() != null ? request.getVendor() : "");
		pntDevice.setBdChipSn(request.getBdChipSn() != null ? request.getBdChipSn() : "");
		pntDevice.setDeviceType(DeviceTypeEnum.PNT.getSymbol());
		pntDevice.setSpecificity(request.getSpecificity() != null ? request.getSpecificity() : 0);
		pntDevice.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		pntDevice.setCategory(request.getCategory() != null ? request.getCategory() : 0);
		pntDevice.setDeviceNum(request.getDeviceNum() != null ? request.getDeviceNum() : "");
		pntDevice.setRefSource(request.getRefSource() != null ? request.getRefSource() : "");
		pntDevice.setInstalldate(request.getInstalldate() != null ? request.getInstalldate() : null);
		pntDevice.setCreateTime(request.getCreateTime() != null ? request.getCreateTime() : null);
		pntDevice.setUpdateTime(request.getUpdateTime() != null ? request.getUpdateTime() : new Date());
		pntDevice.setDeleted(request.getDeleted() != null ? request.getDeleted() : 0);
		pntDevice.setClockSignal(request.getClockSignal() != null ? request.getClockSignal() : "");
		pntDevice.setScenario(request.getScenario() != null ? request.getScenario() : 0);
		pntDevice.setDomain(request.getDomain() != null ? request.getDomain() : 0);
		pntDevice.setGnssMode(request.getGnssMode() != null ? request.getGnssMode() : 0);
		pntDevice.setIotProtocol(request.getIotProtocol() != null ? request.getIotProtocol() : IotProtocolEnum.JT808.getCode());
		pntDevice.setTerminalId(request.getTerminalId() != null ? request.getTerminalId() : "");
		pntDevice.setAssetType(request.getAssetType() != null ? request.getAssetType() : 0);
		pntDevice.setOwnDeptType(request.getOwnDeptType() != null ? request.getOwnDeptType() : 0);
		pntDevice.setOwnDeptName(request.getOwnDeptName() != null ? request.getOwnDeptName() : "");
		return pntDevice;
	}

	@Override
	public int bindTarget(Long deviceId, Long targetId, Integer targetType,String targetName){
		return this.pntDeviceMapper.bindTarget(deviceId,targetId, targetType,targetName);
	}

}
