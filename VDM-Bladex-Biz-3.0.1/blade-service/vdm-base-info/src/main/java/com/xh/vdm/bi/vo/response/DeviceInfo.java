package com.xh.vdm.bi.vo.response;

import lombok.Data;
import org.springblade.common.enums.Operation;

/**
 * Description: 北斗定位设备或者车辆目标信息
 */
@Data
public class DeviceInfo {

	private Operation operation;
	//设备id
	private Long deviceId;
	//设备类型
	private Integer deviceType;
	//设备赋码值
	private String deviceNum;
	//设备手机号
	private String deviceNo;
	//设备型号
	private String deviceModel;
	//序列号
	private String deviceUniqueId;
	//目标id
	private Long targetId;
	//目标类型
	private Integer targetType;
	//目标名称
	private String targetName;
	//目标编号
	private String targetNo;
	//目标种类
	private Integer targetCategory;
	//特殊性，1-旧设备，2-新设备，3-特殊设备
	private Integer specificity;
	//设备所属单位id
	private Long deptId;
}
