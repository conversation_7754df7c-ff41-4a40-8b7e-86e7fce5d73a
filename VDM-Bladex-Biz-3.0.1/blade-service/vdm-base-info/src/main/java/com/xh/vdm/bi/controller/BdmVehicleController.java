package com.xh.vdm.bi.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.utils.AuthUtils;
import com.xh.vdm.bi.utils.DictUtil;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.PersonTerminalRequest;
import com.xh.vdm.bi.vo.request.VehicleRequest;
import com.xh.vdm.bi.vo.request.VehicleTerminalRequest;
import com.xh.vdm.bi.vo.response.ConnectResponse;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.VehicleResponse;
import com.xh.vdm.biapi.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springblade.common.annotation.Log;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.minio.MinioService;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.EasyExcelUtils;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 车辆管理
 */
@RestController
@Slf4j
@RequestMapping("/baseinfo/vehicle")
public class BdmVehicleController {

	@Resource
	private IBdmVehicleService iBdmVehicleService;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private MinioService minioService;
	@Resource
	private RnssDeviceService rnssDeviceService;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private IBdmDeviceStatusService deviceStatusService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;

	@Resource
	private IBdmAbstractTargetService abstractTargetService;
	@Resource
	private IBdmVirtualTargetService virtualTargetService;
	@Resource
	private CETokenUtil ceTokenUtil;
	@Resource
	private DictUtil dictUtil;

	/**
	 * 分页查询
	 *
	 * @param vehicleRequest 筛选条件
	 * @return 查询结果
	 */
	//auth_test: 权限验证通过
	@PostMapping("/list")
	public R<IPage<VehicleResponse>> queryByPage(@RequestBody VehicleRequest vehicleRequest, BladeUser user) {
		String account = AuthUtil.getUserAccount();
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		IPage<VehicleResponse> page = this.iBdmVehicleService.queryByPage(vehicleRequest, ceDataAuth);
		enrichDataWithDict(page.getRecords());
		return R.data(page);
	}

	/**
	 * 新增数据
	 *
	 * @param vehicleRequest 实体
	 * @return 新增结果
	 */
	//auth_test:测试通过
	@Log(menu = "车辆信息管理", operation = Operation.INSERT, objectType = ObjectType.VEHICLE)
	@PostMapping("/save")
	public R add(@Valid @RequestBody VehicleRequest vehicleRequest, BladeUser user) {
		//获取当前用户用户名
		String account = AuthUtil.getUserAccount();
		vehicleRequest.setCreateAccount(account);
		BdmVehicle vehicle = this.iBdmVehicleService.insert(vehicleRequest);
		if (vehicle != null) {
			return R.data(ResultCode.SUCCESS.getCode(), vehicle.getId().toString(), "新增成功");
		}
		return R.fail("新增失败");
	}

	/**
	 * 编辑数据
	 *
	 * @param vehicleRequest 实体
	 * @return 编辑结果
	 */
	@Log(menu = "车辆信息管理", operation = Operation.UPDATE, objectType = ObjectType.VEHICLE)
	@PostMapping("/update")
	public R edit(@Valid @RequestBody VehicleRequest vehicleRequest, BladeUser user) {
		BdmVehicle vehicleInDB = this.iBdmVehicleService.getById(vehicleRequest.getId());

		BdmVehicle bdmVehicle = new BdmVehicle();
		BeanUtils.copyProperties(vehicleRequest, bdmVehicle);
		BdmVehicle vehicle = this.iBdmVehicleService.update(vehicleRequest);

		if (vehicle != null) {
			String result = new CompareUtils<BdmVehicle>().compare(vehicleInDB, bdmVehicle);
			return R.data(ResultCode.SUCCESS.getCode(), result, "编辑成功");
		}
		return R.fail("编辑失败");
	}

	/**
	 * 批量编辑数据
	 *
	 * @param batchUpdateRequest 实体
	 * @return 编辑结果
	 */
	@Log(menu = "车辆信息管理", operation = Operation.UPDATE, objectType = ObjectType.VEHICLE)
	@PostMapping("/batchUpdate")
	public R batchUpdate(@RequestBody BatchUpdateRequest batchUpdateRequest) {
		if (CollectionUtils.isEmpty(batchUpdateRequest.getIds())) {
			return R.fail("参数不能为空");
		}
		if (null == batchUpdateRequest.getDeptId()) {
			return R.fail("所属机构不能为空");
		}

		Boolean b = iBdmVehicleService.batchUpdate(batchUpdateRequest);
		if (b){
			return R.data(ResultCode.SUCCESS.getCode(), "编辑成功");
		}else {
			return R.fail("操作失败");
		}

	}

	/**
	 * 删除数据
	 *
	 * @param ids 主键
	 * @return 删除是否成功
	 */
	@Log(menu = "车辆信息管理", operation = Operation.DELETE, objectType = ObjectType.VEHICLE)
	@GetMapping("/delete")
	public R deleteByIds(@RequestParam("ids") Long[] ids, BladeUser user) {
		QueryWrapper<BdmAbstractDevice> badQw = new QueryWrapper<>();
		badQw.in("target_id", ids);
		badQw.eq("deleted", 0);
		List<BdmAbstractDevice> badList = abstractDeviceService.getBaseMapper().selectList(badQw);
		if (!badList.isEmpty()){
			return R.fail("该车辆已绑定终端，请先解绑！");
		}
		Map<Long, Object> map = new HashMap<>();
		List<String> keys = new ArrayList<>();
		for (Long id : ids) {
			keys.add(BaseInfoConstants.VEHICLE_TARGET_TYPE + "-" + id);
		}
		// 获取 Redis 数据
		List<Object> values = redisTemplate.opsForHash().multiGet(BaseInfoConstants.BASEINFO_TARGET, keys);
		// 处理获取到的值
		for (int i = 0; i < keys.size(); i++) {
			String key = keys.get(i);
			Object value = values.get(i);
			if (value != null) {
				String jsonValue = value.toString();
				BdmVehicle vehicle = JSONObject.parseObject(jsonValue, BdmVehicle.class);

				int index = key.indexOf("-");
				if (index != -1) {
					String idValue = key.substring(index + 1);
					Long deptId = vehicle.getDeptId();
					if (map.containsKey(deptId)) {
						String existingValues = (String) map.get(deptId);
						map.put(deptId, existingValues + "、" + idValue);
					} else {
						map.put(deptId, idValue);
					}
				}
			}
		}
		boolean result = this.iBdmVehicleService.deleteByIds(ids);
		if (result) {
			return R.data(ResultCode.SUCCESS.getCode(), map, "删除成功");
		} else {
			return R.fail("删除失败");
		}
	}

	/**
	 * 导出
	 */
	@PostMapping("/export")
	public R<String> export(@RequestBody VehicleRequest vehicleRequest, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		try {
			vehicleRequest.setCurrent(1);
			vehicleRequest.setSize(Integer.MAX_VALUE);
			IPage<VehicleResponse> list = this.iBdmVehicleService.queryByPage(vehicleRequest, ceDataAuth);
			if (list.getRecords().isEmpty()) {
				return R.fail("没有数据可导出");
			}

			Map<String, String> map = new HashMap<>();
			//从字典中获取数据
			enrichDataWithDict(list.getRecords());
			for (VehicleResponse response : list.getRecords()) {
				String key = BaseInfoConstants.VEHICLE_TARGET_TYPE + "-" + response.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", response.getNumber());
				innerMap.put("targetType", response.getTargetType());
				innerMap.put("targetCategory", response.getCategory());
				innerMap.put("deptId", response.getDeptId());
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			}
			// todo：Map<String, String> map这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			String menu = "车辆管理";
			try {
				// 使用ByteArrayOutputStream创建Excel文件
				ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
				EasyExcelUtils.exportExcelToStream(
					menu,
					outputStream,
					(List) list.getRecords(),
					vehicleRequest.getHeadNameList(),
					vehicleRequest.getColumnNameList(),
					VehicleResponse.class
				);
				ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
				String minioFileUrl = minioService.uploadFile(menu, inputStream, outputStream.size());
				if (minioFileUrl != null) {
					return R.data(minioFileUrl);
				} else {
					return R.fail("文件上传失败");
				}
			} catch (Exception e) {
				log.info("文件上传失败 " + e.getMessage());
				return R.fail("文件上传失败");
			}
		} catch (Exception e) {
			log.error("导出失败", e);
			return R.fail("导出失败");
		}
	}

	/**
	 * 导入数据
	 */
	@Log(menu = "车辆信息管理", operation = Operation.IMPORT, objectType = ObjectType.VEHICLE)
	@PostMapping("/importExcel")
	public R importExcel(@Valid @RequestBody List<VehicleRequest> list, BladeUser user) {
		if (list.isEmpty()) {
			return R.fail("数据为空");
		}
		List<VehicleRequest> result = this.iBdmVehicleService.importExcel(list);
		if (result.isEmpty()) {
			Map<Long, StringBuilder> deptIdMap = new HashMap<>();
			for (VehicleRequest request : list) {
				Long deptId = request.getDeptId();
				Long id = request.getId();
				if (deptIdMap.containsKey(deptId)) {
					deptIdMap.get(deptId).append("、").append(id);
				} else {
					deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
				}
			}
			return R.data(ResultCode.SUCCESS.getCode(), deptIdMap, "导入成功");
		} else {
			String filePath = "";
			String menu = "车辆管理";
			try {
				filePath = minioService.exportToMinIO(menu, result, VehicleRequest.class);
			} catch (IOException e) {
				log.error("导出错误数据失败");
			}
			List<VehicleRequest> filteredList = list.stream()
				.filter(item -> !result.contains(item))
				.collect(Collectors.toList());
			Map<Long, StringBuilder> deptIdMap = new HashMap<>();
			if (!filteredList.isEmpty()) {
				for (VehicleRequest request : filteredList) {
					Long deptId = request.getDeptId();
					Long id = request.getId();
					if (deptIdMap.containsKey(deptId)) {
						deptIdMap.get(deptId).append("、").append(id);
					} else {
						deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
					}
				}
			}
			return R.data(207, deptIdMap, filePath);
		}
	}

	private void enrichDataWithDict(List<VehicleResponse> records) {
		if (records.isEmpty()) {
			return;
		}
		Map<String, String> deviceTypeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_DEVICE_TYPE);
		Map<String, String> carMap = dictUtil.getDictMap(BaseInfoConstants.BDM_CAR_CATEGORY);
		Map<String, String> typeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_WORKER_TYPE);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		for (VehicleResponse response : records) {
			Date date = null;
			try {
				date = sdf.parse(response.getCreateTime());
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}
			response.setCreateTime(sdf.format(date));
			response.setCategoryName(carMap.getOrDefault(response.getCategory(), null));
			response.setTargetTypeName(typeMap.getOrDefault(response.getTargetType(), null));

			if (StringUtils.isNotBlank(response.getTerminalCategories())) {
				String[] categories = response.getTerminalCategories().split(",");
				if (categories.length > 0) {
					response.setTerminalCategories(deviceTypeMap.get(categories[0]));
				}
			}
		}
	}

	/**
	 * 解除绑定
	 */
	@GetMapping("/unbind")
	@Transactional
	@Log(menu = "车辆信息管理", operation = Operation.BIND_OR_UNBIND, objectType = ObjectType.VEHICLE)
	public R unbind(@RequestParam Long id,@RequestParam Integer targetType, BladeUser user, @DeptId Long deptId){
		//BdmRnssDevice device = rnssDeviceService.getOne(new QueryWrapper<BdmRnssDevice>().eq("target_id", id).eq("target_type", targetType).eq("deleted", 0));
		BdmAbstractDevice device = abstractDeviceService.getOne(new QueryWrapper<BdmAbstractDevice>().eq("target_id", id).eq("target_type", targetType).eq("deleted", 0));
		if (device==null){
			return R.fail("未查询到与该车辆绑定的抽象终端");
		}
		//BdmAbstractTarget target =abstractTargetService.getOne(new QueryWrapper<BdmAbstractTarget>().eq("number", device.getUniqueId()).eq("deleted", 0));
		BdmAbstractTarget target =abstractTargetService.getOne(new QueryWrapper<BdmAbstractTarget>().eq("number", device.getUniqueId()));
		if (target==null){
			return R.fail("未查询到与该车辆绑定的抽象终端的终端目标");
		}
		//int row = rnssDeviceService.unbind(id, targetType);
		int rnssRow = rnssDeviceService.bindTarget(device.getId(),target.getId(),target.getTargetType(),target.getName());
		int badRow=abstractDeviceService.bindTarget(device.getId(),target.getId(),target.getTargetType(),target.getName());
		if (rnssRow > 0 && badRow > 0){
			QueryWrapper<BdmDeviceStatus> wrapper = new QueryWrapper<>();
			wrapper.eq("device_id", device.getId());
			wrapper.eq("device_type",device.getDeviceType());
			wrapper.eq("target_id",id);
			wrapper.eq("target_type",targetType);
			deviceStatusService.remove(wrapper);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setTargetId(id);
			deviceInfo.setTargetType(targetType);
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_vehicle_new", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("车辆管理解绑更新消息发送到kafka失败", e);
			}
			//abstractDeviceService.unbinding(id, TargetTypeEnum.VEHICLE.getSymbol());
			// 恢复待分配终端对象记录
			//virtualTargetService.updateStatus(device.getUniqueId());
			virtualTargetService.restoreStatus(target.getId(),0);
			abstractTargetService.restoreStatus(target.getId(), 0);
			return R.success("解绑成功");
		}
		return R.fail("解绑失败");
	}

	/**
	 * 绑定终端
	 */
	@Log(menu = "车辆信息管理", operation = Operation.BIND_OR_UNBIND, objectType = ObjectType.VEHICLE)
	@PostMapping("/connect")
	public R<T> connectTerminal(@RequestBody List<VehicleTerminalRequest> list, Long id, @DeptId Long deptId, BladeUser user) {
		if (id == null) {
			return R.fail("车辆id不能为空");
		}
		boolean result = this.iBdmVehicleService.connectTerminal(list, id, deptId);
		if (!result) {
			return R.fail(ResultCode.FAILURE, "车辆类型只能绑定一个设备！");
		}
		return R.success("操作成功");
	}

	@Log(menu = "车辆信息管理", operation = Operation.BIND_OR_UNBIND, objectType = ObjectType.VEHICLE)
	@PostMapping("/connectuniqueid")
	public R<T> connectUniqueId(@RequestBody VehicleTerminalRequest vtq, Long id, @DeptId Long deptId, BladeUser user) {
		if (id == null) {
			return R.fail("车辆id不能为空");
		}
		ConnectResponse res = this.iBdmVehicleService.connectUniqueId(vtq.getUniqueId(), id, deptId);
		if (res.getCode()!=0) {
			return R.fail(ResultCode.FAILURE, res.getMsg());
		}
		return R.success("操作成功");
	}
}

