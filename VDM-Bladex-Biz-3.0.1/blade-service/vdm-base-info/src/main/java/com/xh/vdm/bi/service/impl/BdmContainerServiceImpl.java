package com.xh.vdm.bi.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.converter.BdmAbstractTargetConverter;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.mapper.BdmContainerMapper;
import com.xh.vdm.bi.service.BdmContainerService;
import com.xh.vdm.bi.service.IBdmAbstractDeviceService;
import com.xh.vdm.bi.service.IBdmAbstractTargetService;
import com.xh.vdm.bi.service.RnssDeviceService;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.ContainerRequest;
import com.xh.vdm.bi.vo.response.ContainerResponse;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import com.xh.vdm.biapi.entity.BdmContainer;
import com.xh.vdm.biapi.entity.BdmVehicle;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 集装箱管理
 */
@Service
public class BdmContainerServiceImpl extends ServiceImpl<BdmContainerMapper, BdmContainer> implements BdmContainerService {
	@Resource
	private BdmContainerMapper bdmContainerMapper;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private RnssDeviceService rnssDeviceService;
	@Resource
	private IBdmAbstractTargetService abstractTargetService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
     * 分页查询
     *
     * @param container  筛选条件
     * @param query      分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	@Override
	public IPage<ContainerResponse> queryByPage(ContainerRequest container, Query query, DataAuthCE ceDataAuth) {
		IPage<ContainerResponse> page = new Page<>(query.getCurrent(), query.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.bdmContainerMapper.queryByPage(container, page, response.getAccount(), response.getOrgList());
	}

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmContainer insert(ContainerRequest request) {
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.TEMPORARY_TARGET_TYPE, 0, 0);
		request.setId(targetId.nextId());
		request.setCreateTime(new Date());
		request.setCreateAccount(AuthUtil.getUserAccount());
		this.bdmContainerMapper.insert(request);

		BdmContainer container = this.bdmContainerMapper.selectById(request.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(container, abstractTarget);
		abstractTarget.setName(container.getNumber());
		abstractTarget.setCreateAccount(AuthUtil.getUserAccount());
		abstractTargetService.save(abstractTarget);

		Map<String, String> map = new HashMap<>();
		String key = BaseInfoConstants.CONTAINER_TARGET_TYPE + "-" + container.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", container.getNumber());
		innerMap.put("targetType", container.getTargetType());
		innerMap.put("deptId", container.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setTargetId(container.getId());
		deviceInfo.setTargetName(container.getNumber());
		deviceInfo.setTargetType(container.getTargetType());
		deviceInfo.setDeptId(container.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_container", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("集装箱信息更新消息发送到kafka失败", e);
		}
		return container;
	}

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmContainer update(ContainerRequest request) {
		BdmContainer containerInDB = baseMapper.selectById(request.getId());

		BdmContainer bdmContainer = new BdmContainer();
		bdmContainer.setId(request.getId());
		bdmContainer.setNumber(request.getNumber() != null ? request.getNumber() : "");
		bdmContainer.setModel(request.getModel() != null ? request.getModel() : 0);
		bdmContainer.setSize(request.getSize() != null ? request.getSize() : 0);
		bdmContainer.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		bdmContainer.setMaxGross(request.getMaxGross() != null ? request.getMaxGross() : 0.0f);
		bdmContainer.setTare(request.getTare() != null ? request.getTare() : 0.0f);
		bdmContainer.setNet(request.getNet() != null ? request.getNet() : 0.0f);
		bdmContainer.setCuCap(request.getCuCap() != null ? request.getCuCap() : 0);
		bdmContainer.setLength(request.getLength() != null ? request.getLength() : 0);
		bdmContainer.setHeight(request.getHeight() != null ? request.getHeight() : 0);
		bdmContainer.setUpdateTime(new Date());
		bdmContainer.setTargetType(request.getTargetType() != null ? request.getTargetType() : TargetTypeEnum.RECEIVER.getSymbol());
		this.bdmContainerMapper.update(bdmContainer);

		BdmContainer container = baseMapper.selectById(bdmContainer.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(container, abstractTarget);
		abstractTarget.setName(container.getNumber());
		abstractTargetService.updateById(abstractTarget);

		String key = container.getTargetType() + "-" + container.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", container.getNumber());
		innerMap.put("targetType", container.getTargetType());
		innerMap.put("deptId", container.getDeptId());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setTargetId(container.getId());
		deviceInfo.setTargetName(container.getNumber());
		deviceInfo.setTargetType(container.getTargetType());
		deviceInfo.setDeptId(container.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_container", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("集装箱信息更新消息发送到kafka失败", e);
		}

		//@zhouxw modify 2025-06-27 修改监管对象，终端不再同步更改组织
		/*if(!containerInDB.getDeptId().equals(container.getDeptId())){
			// 对于RnssDevice的更新
			rnssDeviceService.updateDept(container.getId(), container.getTargetType(), container.getDeptId());

			// 对于BdmAbstractDevice的更新
			abstractDeviceService.updateDept(container.getId(), container.getTargetType(), container.getDeptId());
		}*/

		return container;
	}

	/**
	 * 通过主键删除数据
	 *
	 * @param ids 主键
	 * @return 是否成功
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIds(Long[] ids) {
		boolean result = this.bdmContainerMapper.deleteByIds(ids) > 0;
		if (result) {
			for (Long id : ids) {
				String key = BaseInfoConstants.CONTAINER_TARGET_TYPE + "-" + id;
				redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET + ":" + key);
			}
			this.rnssDeviceService.deleteByTargetIds(ids, TargetTypeEnum.RECEIVER.getSymbol());

			DeviceInfo deviceInfo = new DeviceInfo();
			Long lastId = ids[ids.length - 1];
			deviceInfo.setTargetId(lastId);
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_container", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("集装箱信息更新消息发送到kafka失败", e);
			}
			//更新bdm_abstract_target
			abstractTargetService.deleteByIds(ids);
			//更新bdm_abstract_device
			abstractDeviceService.deleteByTargetIds(ids, TargetTypeEnum.VEHICLE.getSymbol());
		}
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<ContainerRequest> importExcel(List<ContainerRequest> list) {
		//错误的的数据
		List<ContainerRequest> duplicateRequests = getDuplicateRequests(list);
		List<ContainerRequest> requests = new ArrayList<>(list);
		// 去重后的数据（去除所有重复数据）
		requests.removeAll(duplicateRequests);

		if (!requests.isEmpty()) {
			List<String> numberList = requests.stream()
					.map(ContainerRequest::getNumber)
					.collect(Collectors.toList());
			//数据库的数据
			QueryWrapper<BdmContainer> queryWrapper = new QueryWrapper<>();
			if (!numberList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(queryWrapper, "number", numberList);
			}
			queryWrapper.eq("deleted",0);
			queryWrapper.select("number");
			List<String> numberExitList = this.bdmContainerMapper.selectList(queryWrapper)
					.stream()
					.map(BdmContainer::getNumber)
					.collect(Collectors.toList());

			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.TEMPORARY_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			List<BdmAbstractTarget> bdmAbstractTargets = new ArrayList<>();
			List<BdmContainer> containerList = new ArrayList<>();
            Map<String, String> map = new HashMap<>();

            requests.forEach(c -> {
                if (numberExitList.contains(c.getNumber())) {
                    c.setMsg("集装箱编号已存在");
                    duplicateRequests.add(c);
                } else {
                    BdmContainer container = new BdmContainer();
                    container.setId(targetId.nextId());
                    container.setNumber(c.getNumber() != null ? c.getNumber() : "");
                    container.setModel(c.getModel() != null ? c.getModel() : 0);
                    container.setSize(c.getSize() != null ? c.getSize() : 0);
                    container.setDeptId(c.getDeptId() != null ? c.getDeptId() : 0);
                    container.setMaxGross(c.getMaxGross() != null ? c.getMaxGross() : 0.0f);
                    container.setTare(c.getTare() != null ? c.getTare() : 0.0f);
                    container.setNet(c.getNet() != null ? c.getNet() : 0.0f);
                    container.setCuCap(c.getCuCap() != null ? c.getCuCap() : 0);
                    container.setLength(c.getLength() != null ? c.getLength() : 0);
                    container.setHeight(c.getHeight() != null ? c.getHeight() : 0);
                    container.setCreateTime(new Date());
                    container.setDeleted(0);
                    container.setTargetType(TargetTypeEnum.RECEIVER.getSymbol());
                    containerList.add(container);
                    c.setId(container.getId());

                    String key = BaseInfoConstants.WORKER_TARGET_TYPE + "-" + container.getId();
                    Map<String, Object> innerMap = new HashMap<>();
                    innerMap.put("targetName", container.getNumber());
                    innerMap.put("targetType", container.getTargetType());
                    innerMap.put("deptId", container.getDeptId());
                    try {
                        map.put(key, new ObjectMapper().writeValueAsString(innerMap));
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                    }

                    BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
                    BeanUtils.copyProperties(container, abstractTarget);
                    abstractTarget.setName(container.getNumber());
					abstractTarget.setCategory(0);
                    bdmAbstractTargets.add(abstractTarget);
                }
            });
            if (!containerList.isEmpty()) {
                MapperUtils.splitListByCapacity(containerList, MapperUtils.DEFAULT_CAPACITY)
                        .forEach(bdmIotCards -> this.bdmContainerMapper.insertBatch(bdmIotCards));

                MapperUtils.splitListByCapacity(bdmAbstractTargets, MapperUtils.DEFAULT_CAPACITY)
                        .forEach(abstractTargetList -> this.abstractTargetService.insertBatch(abstractTargetList));

                if (!map.isEmpty()) {
                    redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);
                }
                BdmContainer lastContainer = containerList.get(containerList.size() - 1);
                DeviceInfo deviceInfo = new DeviceInfo();
                deviceInfo.setTargetId(lastContainer.getId());
                deviceInfo.setTargetName(lastContainer.getNumber());
                deviceInfo.setDeptId(lastContainer.getDeptId());
                try {
                    this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_container", JSON.toJSONString(deviceInfo));
                } catch (Exception e) {
                    log.error("集装箱信息更新消息发送到kafka失败", e);
                }
            }
		}
		return duplicateRequests;
	}

	/**
	 *
	 * @param batchUpdateRequest
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest) {
		int update = bdmContainerMapper.batchUpdate(batchUpdateRequest.getIds(),batchUpdateRequest.getDeptId());
		if (update == 0) {
			return false;
		}
		//查询出所有的本次要更新的字段
		List<BdmContainer> bdmContainerList = bdmContainerMapper.selectList(new LambdaQueryWrapper<BdmContainer>().in(BdmContainer::getId, batchUpdateRequest.getIds()));
		//转换
		List<BdmAbstractTarget> bdmAbstractTargetList = bdmContainerList.stream().map(BdmAbstractTargetConverter::toBdmContainer).collect(Collectors.toList());
		//更新主表
		abstractTargetService.saveOrUpdateBatch(bdmAbstractTargetList);
		//批量更新redis和kafka
		batchProcess(bdmContainerList);
		if (update > 0){
			return true;
		}
		return false;
	}

	/**
	 * 批量更新redis
	 * 批量发送kafka
	 */
	private void batchProcess(List<BdmContainer> bdmContainerList){
		for (BdmContainer container : bdmContainerList) {
			String key = container.getTargetType() + "-" + container.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("targetName", container.getNumber());
			innerMap.put("targetType", container.getTargetType());
			innerMap.put("deptId", container.getDeptId());
			Map<String, String> map = new HashMap<>();
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setTargetId(container.getId());
			deviceInfo.setTargetName(container.getNumber());
			deviceInfo.setTargetType(container.getTargetType());
			deviceInfo.setDeptId(container.getDeptId());
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_container", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("集装箱信息更新消息发送到kafka失败", e);
			}
		}
	}

	/**
	 * 重复数据
	 *
	 * @param list
	 * @return
	 */
	public List<ContainerRequest> getDuplicateRequests(List<ContainerRequest> list) {
		Map<String, Long> CountMap = list.stream()
			.map(ContainerRequest::getNumber)
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

		List<String> duplicates = CountMap.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		return list.stream()
			.filter(request -> duplicates.contains(request.getNumber()))
			.peek(request -> request.setMsg("集装箱编号重复"))
			.collect(Collectors.toList());
	}
}
