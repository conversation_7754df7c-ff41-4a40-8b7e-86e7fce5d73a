package com.xh.vdm.bi.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.converter.BdmAbstractTargetConverter;
import com.xh.vdm.bi.enums.DeviceTypeEnum;
import com.xh.vdm.bi.enums.IotProtocolEnum;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.enums.WearCategoryEnum;
import com.xh.vdm.bi.mapper.BdmVisitorMapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.utils.AuthUtils;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.*;
import com.xh.vdm.bi.vo.response.ConnectResponse;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.VisitorResponse;
import com.xh.vdm.biapi.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (BdmVisitor)表服务实现类
 */
@Service
@Slf4j
public class BdmVisitorServiceImpl extends ServiceImpl<BdmVisitorMapper, BdmVisitor> implements BdmVisitorService {
	@Resource
	private BdmVisitorMapper bdmVisitorMapper;
	@Resource
	private WearableDeviceService wearableDeviceService;
	@Resource
	private RdssDeviceService rdssDeviceService;
	@Resource
	private RnssDeviceService rnssDeviceService;
	@Resource
	private MonitDeviceService monitDeviceService;
	@Resource
	private PntDeviceService pntDeviceService;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private IBdmDeviceStatusService deviceStatusService;
	@Resource
	private IBdmAbstractTargetService abstractTargetService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;
	@Resource
	private IBdmVirtualTargetService virtualTargetService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
     * 分页查询
     *
     * @param request    筛选条件
     * @param query      分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	@Override
	public IPage<VisitorResponse> queryByPage(VisitorRequest request, Query query, DataAuthCE ceDataAuth) {
		IPage<VisitorResponse> page = new Page<>(query.getCurrent(), query.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.bdmVisitorMapper.queryByPage(request, page, response.getAccount(), response.getOrgList());
	}


	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmVisitor insert(VisitorRequest request) {
		Map<String, String> map = new HashMap<>();
		//访客填写的身份证号
		boolean result = judgeId(request.getIdNumber());
		if (!result) {
			throw new RuntimeException("身份证号填写不合法！");
		}
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VISITOR_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		request.setId(targetId.nextId());
		request.setCreateTime(new Date());
		String account = AuthUtil.getUserAccount();
		request.setCreateAccount(account);
		this.bdmVisitorMapper.insert(request);

		BdmVisitor visitor = this.getBaseMapper().selectById(request.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(visitor, abstractTarget);
		abstractTarget.setNumber(visitor.getIdNumber());
		abstractTarget.setCreateAccount(AuthUtil.getUserAccount());
		abstractTargetService.save(abstractTarget);

		String key = BaseInfoConstants.VISITOR_TARGET_TYPE + "-" + visitor.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", visitor.getName() + "(" + visitor.getIdNumber() + ")");
		innerMap.put("targetType", TargetTypeEnum.VISITOR.getSymbol());
		innerMap.put("deptId", visitor.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setTargetId(visitor.getId());
		deviceInfo.setTargetName(visitor.getName());
		deviceInfo.setDeptId(visitor.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_visitor", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("访客管理信息更新消息发送到kafka失败", e);
		}
		return visitor;
	}

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmVisitor update(VisitorRequest request) {
		BdmVisitor visitorInDB = this.getBaseMapper().selectById(request.getId());

		BdmVisitor bdmVisitor = new BdmVisitor();
		bdmVisitor.setId(request.getId());
		bdmVisitor.setName(request.getName() != null ? request.getName() : "");
		bdmVisitor.setIdNumber(request.getIdNumber() != null ? request.getIdNumber() : "");
		bdmVisitor.setTargetType(request.getTargetType() != null ? request.getTargetType() : TargetTypeEnum.VISITOR.getSymbol());
		bdmVisitor.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		bdmVisitor.setPost(request.getPost() != null ? request.getPost() : 0);
		bdmVisitor.setIndustry(request.getIndustry() != null ? request.getIndustry() : 0);
		bdmVisitor.setPhone(request.getPhone() != null ? request.getPhone() : "");
		bdmVisitor.setCompany(request.getCompany() != null ? request.getCompany() : "");
		bdmVisitor.setValidFrom(request.getValidFrom() != null ? request.getValidFrom() : null);
		bdmVisitor.setValidTo(request.getValidTo() != null ? request.getValidTo() : null);
		bdmVisitor.setUpdateTime(new Date());
		this.bdmVisitorMapper.update(bdmVisitor);

		BdmVisitor visitor = this.getBaseMapper().selectById(bdmVisitor.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(visitor, abstractTarget);
		abstractTarget.setNumber(visitor.getIdNumber());
		abstractTargetService.updateById(abstractTarget);

		String key = BaseInfoConstants.VISITOR_TARGET_TYPE + "-" + visitor.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", visitor.getName() + "(" + visitor.getIdNumber() + ")");
		innerMap.put("targetType", visitor.getTargetType());
		innerMap.put("deptId", visitor.getDeptId());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setTargetId(visitor.getId());
		deviceInfo.setTargetName(visitor.getName());
		deviceInfo.setTargetType(visitor.getTargetType());
		deviceInfo.setDeptId(visitor.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_visitor", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("访客管理信息更新消息发送到kafka失败", e);
		}

		//@zhouxw modify 2025-06-27 修改监管对象，终端不再同步更改组织
		/*if (!visitorInDB.getDeptId().equals(visitor.getDeptId())) {
			// 对于WearableDevice的更新
			wearableDeviceService.updateDept(visitor.getId(), visitor.getTargetType(), visitor.getDeptId());

			// 对于RdssDevice的更新
			rdssDeviceService.updateDept(visitor.getId(), visitor.getTargetType(), visitor.getDeptId());

			// 对于RnssDevice的更新
			rnssDeviceService.updateDept(visitor.getId(), visitor.getTargetType(), visitor.getDeptId());

			// 对于BdmAbstractDevice的更新
			abstractDeviceService.updateDept(visitor.getId(), visitor.getTargetType(), visitor.getDeptId());
		}*/

		return visitor;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIds(Long[] ids) {
		boolean result = this.bdmVisitorMapper.deleteByIds(ids) > 0;
		if (result) {
			for (Long id : ids) {
				String key = BaseInfoConstants.VISITOR_TARGET_TYPE + "-" + id;
				redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET + ":" + key);
			}
			this.wearableDeviceService.deleteByTargetIds(ids, TargetTypeEnum.VISITOR.getSymbol());
			this.rdssDeviceService.deleteByTargetIds(ids, TargetTypeEnum.VISITOR.getSymbol());
			this.rnssDeviceService.deleteByTargetIds(ids, TargetTypeEnum.VISITOR.getSymbol());

			DeviceInfo deviceInfo = new DeviceInfo();
			Long lastId = ids[ids.length - 1];
			deviceInfo.setTargetId(lastId);
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_visitor", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("访客信息更新消息发送到kafka失败", e);
			}
			//更新bdm_abstract_target
			abstractTargetService.deleteByIds(ids);
			//更新bdm_abstract_device
			abstractDeviceService.deleteByTargetIds(ids, TargetTypeEnum.VISITOR.getSymbol());
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ConnectResponse connectTerminal(List<PersonTerminalRequest> list, Long id, Long deptId) {
		ConnectResponse res=new ConnectResponse();
		long rnssNum=list.stream().filter(val->val.getDeviceType().equals(DeviceTypeEnum.RNSS.getSymbol())).count();
		if (rnssNum>1){
			res.setCode(1);
			res.setMsg("访客最多只能绑定一个808终端");
			return res;
		}
		BdmVisitor visitor = baseMapper.selectById(id);
		String targetName = visitor.getName() + "(" + visitor.getIdNumber() + ")";

		List<BdmAbstractDevice> abstractDevices = abstractDeviceService.getBaseMapper()
			.selectList(new QueryWrapper<BdmAbstractDevice>()
				.eq("target_id", id)
				.eq("target_type", TargetTypeEnum.VISITOR.getSymbol())
			);

		List<String> uniqueIdList = new ArrayList<>();
		if(!abstractDevices.isEmpty()){
			//bdm_device_status更新
			for (BdmAbstractDevice request:abstractDevices) {
				QueryWrapper<BdmDeviceStatus> wrapper = new QueryWrapper<>();
				wrapper.eq("device_id", request.getId());
				wrapper.eq("device_type", request.getDeviceType());
				wrapper.eq("target_id", request.getTargetId());
				wrapper.eq("target_type", request.getTargetType());
				deviceStatusService.remove(wrapper);

				BdmAbstractTarget target =abstractTargetService.getOne(new QueryWrapper<BdmAbstractTarget>().eq("number", request.getUniqueId()).eq("deleted", 0));
				if (target==null){
					res.setCode(2);
					res.setMsg("未查询到与该目标绑定的原抽象终端的待分配终端目标");
					return res;
				}

				if (request.getDeviceType().equals(DeviceTypeEnum.WEARABLE.getSymbol())){
					wearableDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				if (request.getDeviceType().equals(DeviceTypeEnum.RNSS.getSymbol())){
					rnssDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				if (request.getDeviceType().equals(DeviceTypeEnum.RDSS.getSymbol())){
					rdssDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				if (request.getDeviceType().equals(DeviceTypeEnum.MONIT.getSymbol())){
					monitDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				if (request.getDeviceType().equals(DeviceTypeEnum.PNT.getSymbol())){
					pntDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				abstractDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());

				uniqueIdList.add(request.getUniqueId());
			}

			// bdm_virtual_target数据 对abstractDevices 进行恢复
			virtualTargetService.updateBatch(uniqueIdList);
		}

		if(list.isEmpty()){
//			//根据用户ID去清空原来绑定的终端数据
//			wearableDeviceService.updateByWorkerId(id, TargetTypeEnum.VISITOR.getSymbol());
//			rdssDeviceService.updateBatchByWorkerId(id, TargetTypeEnum.VISITOR.getSymbol());
//			rnssDeviceService.updateByWorkerId(id, TargetTypeEnum.VISITOR.getSymbol());
//			monitDeviceService.updateByDeviceId(id,TargetTypeEnum.VISITOR.getSymbol());
//			pntDeviceService.updateByDeviceId(id,TargetTypeEnum.VISITOR.getSymbol());
//			//bdm_abstract_device更新绑定关系
//			abstractDeviceService.unbinding(id, TargetTypeEnum.VISITOR.getSymbol());
		}else {
			//PersonTerminalRequest req=list.get(0);
			for (PersonTerminalRequest req:list){
				//后台需要判断设备是否被其他对象绑定，若已被绑定则提示操作失败
				BdmAbstractDevice bad=abstractDeviceService.getBadByUniqueId(req.getUniqueId());
				if (bad==null) {
					res.setCode(2);
					res.setMsg("设备序列号不存在");
					return res;
				}
				if (bad.getTargetId()>0&&bad.getTargetType()>0&&!bad.getTargetId().equals(id)){
					res.setCode(3);
					BdmAbstractTarget bat=abstractTargetService.getById(bad.getTargetId());
					String msg="设备已被其他对象绑定："+bat.getName()+"("+bat.getNumber()+")";
					res.setMsg(msg);
					return res;
				}

				if (DeviceTypeEnum.RDSS.getSymbol().equals(req.getDeviceType())) {
					rdssDeviceService.updateBatchByWorkerId(id, TargetTypeEnum.VISITOR.getSymbol());
					rdssDeviceService.updateBatch(list, id, TargetTypeEnum.VISITOR.getSymbol(), targetName, deptId);
				}
				if (DeviceTypeEnum.WEARABLE.getSymbol().equals(req.getDeviceType())) {
					wearableDeviceService.updateByWorkerId(id, TargetTypeEnum.VISITOR.getSymbol());
					wearableDeviceService.updateBatchByTerminalId(list, id, TargetTypeEnum.VISITOR.getSymbol(), targetName, deptId);
				}
				if (DeviceTypeEnum.RNSS.getSymbol().equals(req.getDeviceType())) {
					rnssDeviceService.updateByWorkerId(id, TargetTypeEnum.VISITOR.getSymbol());
					rnssDeviceService.connectWorkerTerminal(list, id, TargetTypeEnum.VISITOR.getSymbol(), targetName, deptId);
				}
				if (DeviceTypeEnum.MONIT.getSymbol().equals(req.getDeviceType())) {
					monitDeviceService.updateByDeviceId(id,TargetTypeEnum.VISITOR.getSymbol());
					List<FacilityTerminalRequest> pnts =new ArrayList<>();
					FacilityTerminalRequest pnt=new FacilityTerminalRequest();
					pnt.setId(req.getId());
					pnts.add(pnt);
					monitDeviceService.updateBatchByTerminalId(pnts, id, TargetTypeEnum.VISITOR.getSymbol(), deptId);
				}
				if (DeviceTypeEnum.PNT.getSymbol().equals(req.getDeviceType())) {
					pntDeviceService.updateByDeviceId(id,TargetTypeEnum.VISITOR.getSymbol());
					List<FacilityTerminalRequest> pnts =new ArrayList<>();
					FacilityTerminalRequest pnt=new FacilityTerminalRequest();
					pnt.setId(req.getId());
					pnts.add(pnt);
					pntDeviceService.updateBatchByTerminalId(pnts, id, TargetTypeEnum.VISITOR.getSymbol(), deptId);
				}
			}

			abstractDeviceService.unbinding(id, TargetTypeEnum.VISITOR.getSymbol());
			abstractDeviceService.bind(list, id, TargetTypeEnum.VISITOR.getSymbol(), targetName, deptId);

			List<String> uniqueIds = list.stream()
					.map(PersonTerminalRequest::getUniqueId)
					.collect(Collectors.toList());
			// bdm_virtual_target数据 对于list中的808进行删除。
			if(!uniqueIds.isEmpty()){
				virtualTargetService.updateByUniqueId(uniqueIds);
			}
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setTargetId(id);
		deviceInfo.setTargetName(visitor.getName());
		deviceInfo.setTargetType(visitor.getTargetType());
		deviceInfo.setDeptId(visitor.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_visitor", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("访客人员信息更新消息发送到kafka失败", e);
		}
		res.setCode(0);
		return res;
	}

	@Override
	public IPage<PersonNoBingResponse> select(DeviceNoBindRequest deviceNoBindRequest, DataAuthCE ceDataAuth) {
		IPage<PersonNoBingResponse> responseIPage = new Page<>();
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		// bdm_abstract_device 数据进行分页查询
		responseIPage = abstractDeviceService.select(deviceNoBindRequest, response.getAccount(), response.getOrgList());
		return responseIPage;
	}

	@Override
	public List<PersonNoBingResponse> selectBind(DeviceNoBindRequest deviceNoBindRequest, Long id) {
		List<PersonNoBingResponse> resultList = abstractDeviceService.selectBind(id, TargetTypeEnum.VISITOR.getSymbol());
		return resultList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<VisitorRequest> importExcel(List<VisitorRequest> list) {
		//错误的的数据
		List<VisitorRequest> duplicates = getDuplicates(list);

		List<VisitorRequest> requests = new ArrayList<>(list);
		// 去重后的数据（去除所有重复数据）
		requests.removeAll(duplicates);

		if (!requests.isEmpty()) {
			List<String> numberList = requests.stream()
					.map(VisitorRequest::getIdNumber)
					.collect(Collectors.toList());
			//数据库的数据
			QueryWrapper<BdmVisitor> queryWrapper = new QueryWrapper<>();
			if (!numberList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(queryWrapper, "id_number", numberList);
			}
			queryWrapper.gt("DATE(valid_to)", LocalDate.now());
			queryWrapper.eq("deleted",0);
			queryWrapper.select("id_number");
			List<String> numberExitList = this.bdmVisitorMapper.selectList(queryWrapper)
					.stream()
					.map(BdmVisitor::getIdNumber)
					.collect(Collectors.toList());

			List<BdmVisitor> visitorList = new ArrayList<>();
			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VISITOR_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			List<BdmAbstractTarget> bdmAbstractTargets = new ArrayList<>();
			Map<String, String> map = new HashMap<>();

			for (VisitorRequest visitor : requests) {

				boolean result = judgeId(visitor.getIdNumber());
				if (!result) {
					visitor.setMsg("身份证号填写不合法");
					duplicates.add(visitor);
					continue;
				}

				if (numberExitList.contains(visitor.getIdNumber())) {
					visitor.setMsg("身份证号已存在");
					duplicates.add(visitor);
				} else {
					BdmVisitor v = new BdmVisitor();
					v.setId(targetId.nextId());
					v.setName(visitor.getName() != null ? visitor.getName() : "");
					v.setIdNumber(visitor.getIdNumber() != null ? visitor.getIdNumber() : "");
					v.setTargetType(TargetTypeEnum.VISITOR.getSymbol());
					v.setDeptId(visitor.getDeptId() != null ? visitor.getDeptId() : 0);
					v.setPost(visitor.getPost() != null ? visitor.getPost() : 0);
					v.setIndustry(visitor.getIndustry() != null ? visitor.getIndustry() : 0);
					v.setPhone(visitor.getPhone() != null ? visitor.getPhone() : "");
					v.setCompany(visitor.getCompany() != null ? visitor.getCompany() : "");
					v.setValidFrom(visitor.getValidFrom() != null ? visitor.getValidFrom() : null);
					v.setValidTo(visitor.getValidTo() != null ? visitor.getValidTo() : null);
					v.setCreateTime(new Date());
					v.setDeleted(0);
					visitorList.add(v);

					visitor.setId(v.getId());

					String key = BaseInfoConstants.VEHICLE_TARGET_TYPE + "-" + v.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", v.getName() + "(" + v.getIdNumber() + ")");
					innerMap.put("targetType", v.getTargetType());
					innerMap.put("deptId", v.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}

					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(v, abstractTarget);
					abstractTarget.setNumber(v.getIdNumber());
					abstractTarget.setCategory(0);
					bdmAbstractTargets.add(abstractTarget);
				}
			}

			if (!visitorList.isEmpty()) {
				MapperUtils.splitListByCapacity(visitorList, MapperUtils.DEFAULT_CAPACITY)
					.forEach(bdmVisitorList -> this.bdmVisitorMapper.insertBatch(bdmVisitorList));

				MapperUtils.splitListByCapacity(bdmAbstractTargets, MapperUtils.DEFAULT_CAPACITY)
					.forEach(abstractTargetList -> this.abstractTargetService.insertBatch(abstractTargetList));

				redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

				BdmVisitor last = visitorList.get(visitorList.size() - 1);
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setTargetId(last.getId());
				deviceInfo.setTargetName(last.getName());
				deviceInfo.setDeptId(last.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_visitor", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("访客管理信息更新消息发送到kafka失败", e);
				}
			}
		}

		return duplicates;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest) {
		int update = bdmVisitorMapper.batchUpdate(batchUpdateRequest.getIds(),batchUpdateRequest.getDeptId());
		if (update == 0) {
			return false;
		}
		//查询出所有的本次要更新的字段
		List<BdmVisitor> bdmVisitorList = bdmVisitorMapper.selectList(new LambdaQueryWrapper<BdmVisitor>().in(BdmVisitor::getId, batchUpdateRequest.getIds()));
		//转换
		List<BdmAbstractTarget> bdmAbstractTargetList = bdmVisitorList.stream().map(BdmAbstractTargetConverter::toBdmVisitor).collect(Collectors.toList());
		//更新主表
		abstractTargetService.saveOrUpdateBatch(bdmAbstractTargetList);
		//批量更新redis和kafka
		batchProcess(bdmVisitorList);
		if (update > 0){
			return true;
		}
		return false;
	}

	/**
	 * 批量更新redis
	 * 批量发送kafka
	 */
	private void batchProcess(List<BdmVisitor> bdmVisitorList){
		for (BdmVisitor visitor : bdmVisitorList) {
			String key = BaseInfoConstants.VISITOR_TARGET_TYPE + "-" + visitor.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("targetName", visitor.getName() + "(" + visitor.getIdNumber() + ")");
			innerMap.put("targetType", visitor.getTargetType());
			innerMap.put("deptId", visitor.getDeptId());
			Map<String, String> map = new HashMap<>();
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：Map<String, String> map这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setTargetId(visitor.getId());
			deviceInfo.setTargetName(visitor.getName());
			deviceInfo.setTargetType(visitor.getTargetType());
			deviceInfo.setDeptId(visitor.getDeptId());
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_visitor", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("访客管理信息更新消息发送到kafka失败", e);
			}
		}
	}

	// 判断身份证号是否合法
	public static Boolean judgeId(String idNumber) {
		Boolean result = true;

		// 长度不等于 18 位 或 15位
		if (idNumber.length() != 18 && idNumber.length() != 15) {
			return false;
		}

		if (idNumber.length() == 18) {
			// 系数算法
			String tempId = getStr(idNumber, 0, 16);
			int[] coeff = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
			char[] end = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
			int sum = 0;
			for (int i = 0; i < tempId.length(); i++) {
				int bye = tempId.charAt(i) - '0';
				sum += bye * coeff[i];
			}
			sum %= 11;

			if (end[sum] != getStr(idNumber, 17, 17).charAt(0)) {
				result = false;
			}
		}

		return result;
	}

	// 截取字符串的方法
	public static String getStr(String str, int a, int b) {
		b++;
		return str.substring(a, b);
	}

	/**
	 * 重复数据
	 *
	 * @param list
	 * @return
	 */
	public List<VisitorRequest> getDuplicates(List<VisitorRequest> list) {
		Map<String, Long> idNumberMap = list.stream()
			.map(VisitorRequest::getIdNumber)
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

		List<String> duplicateIdNumbers = idNumberMap.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		return list.stream()
			.filter(visitor -> duplicateIdNumbers.contains(visitor.getIdNumber()))
			.peek(visitor -> visitor.setMsg("工卡号/身份证号重复"))
			.collect(Collectors.toList());
	}

}
