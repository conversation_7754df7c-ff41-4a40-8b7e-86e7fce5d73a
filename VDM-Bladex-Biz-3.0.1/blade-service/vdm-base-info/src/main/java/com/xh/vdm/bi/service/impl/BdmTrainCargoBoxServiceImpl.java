package com.xh.vdm.bi.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.converter.BdmAbstractTargetConverter;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.mapper.BdmTrainCargoBoxMapper;
import com.xh.vdm.bi.service.BdmTrainCargoBoxService;
import com.xh.vdm.bi.service.IBdmAbstractDeviceService;
import com.xh.vdm.bi.service.IBdmAbstractTargetService;
import com.xh.vdm.bi.service.RnssDeviceService;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.TrainCargoBoxRequest;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.TrainCargoBoxResponse;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import com.xh.vdm.biapi.entity.BdmTrainCargoBox;
import com.xh.vdm.biapi.entity.BdmVehicle;
import com.xh.vdm.interManager.feign.IMessageClient;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (BdmTrainCargoBox)表服务实现类
 */
@Service
public class BdmTrainCargoBoxServiceImpl extends ServiceImpl<BdmTrainCargoBoxMapper, BdmTrainCargoBox> implements BdmTrainCargoBoxService {
	@Resource
	private BdmTrainCargoBoxMapper bdmTrainCargoBoxMapper;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private IMessageClient messageClient;
	@Resource
	private IBdmAbstractTargetService abstractTargetService;
	@Resource
	private RnssDeviceService rnssDeviceService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
     * 分页查询
     *
     * @param trainCargoBoxRequest 筛选条件
     * @param query                分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	@Override
	public IPage<TrainCargoBoxResponse> queryByPage(TrainCargoBoxRequest trainCargoBoxRequest, Query query, DataAuthCE ceDataAuth) {
		IPage<TrainCargoBoxResponse> page = new Page<>(query.getCurrent(),query.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.bdmTrainCargoBoxMapper.queryAll(trainCargoBoxRequest, page, response.getAccount(), response.getOrgList());
	}

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmTrainCargoBox insert(TrainCargoBoxRequest request) {

		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.BOX_TARGET_TYPE, BaseInfoConstants.ZERO,BaseInfoConstants.ZERO);
		request.setId(targetId.nextId());
		request.setCreateTime(new Date());
		request.setCreateAccount(AuthUtil.getUserAccount());
		this.bdmTrainCargoBoxMapper.insert(request);

		BdmTrainCargoBox cargoBox = this.getBaseMapper().selectById(request.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(cargoBox, abstractTarget);
		abstractTarget.setName(cargoBox.getNumber());
		abstractTarget.setCreateAccount(AuthUtil.getUserAccount());
		abstractTargetService.save(abstractTarget);

		Map<String, String> map = new HashMap<>();
		String key = cargoBox.getTargetType() + "-" + cargoBox.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", cargoBox.getNumber());
		innerMap.put("targetType", cargoBox.getTargetType());
		innerMap.put("deptId", cargoBox.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setTargetId(cargoBox.getId());
		deviceInfo.setTargetName(cargoBox.getNumber());
		deviceInfo.setDeptId(cargoBox.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_train_cargo_box", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("铁路货车车厢信息更新消息发送到kafka失败", e);
		}
		return cargoBox;
	}

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmTrainCargoBox update(TrainCargoBoxRequest request) {
		BdmTrainCargoBox trainCargoBoxInDB = this.getBaseMapper().selectById(request.getId());

		BdmTrainCargoBox trainCargoBox = new BdmTrainCargoBox();
		trainCargoBox.setId(request.getId());
		trainCargoBox.setNumber(request.getNumber() != null ? request.getNumber() : "");
		trainCargoBox.setTargetType(request.getTargetType() != null ? request.getTargetType() : TargetTypeEnum.BOX.getSymbol());
		trainCargoBox.setModel(request.getModel() != null ? request.getModel() : 0);
		trainCargoBox.setSize(request.getSize() != null ? request.getSize() : 0);
		trainCargoBox.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		trainCargoBox.setMaxGross(request.getMaxGross() != null ? request.getMaxGross() : 0);
		trainCargoBox.setTare(request.getTare() != null ? request.getTare() : 0);
		trainCargoBox.setNet(request.getNet() != null ? request.getNet() : 0);
		trainCargoBox.setCuCap(request.getCuCap() != null ? request.getCuCap() : 0);
		trainCargoBox.setLength(request.getLength() != null ? request.getLength() : 0);
		trainCargoBox.setHeight(request.getHeight() != null ? request.getHeight() : 0);
		trainCargoBox.setUpdateTime(new Date());
		this.bdmTrainCargoBoxMapper.update(trainCargoBox);

		BdmTrainCargoBox box = this.getBaseMapper().selectById(trainCargoBox.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(box, abstractTarget);
		abstractTarget.setName(box.getNumber());
		abstractTargetService.updateById(abstractTarget);

		Map<String, String> map = new HashMap<>();
		String key = box.getTargetType() + "-" + box.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", box.getNumber());
		innerMap.put("targetType", box.getTargetType());
		innerMap.put("deptId", box.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setTargetId(box.getId());
		deviceInfo.setTargetName(box.getNumber());
		deviceInfo.setDeptId(box.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_train_cargo_box", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("铁路货车车厢信息更新消息发送到kafka失败", e);
		}

		//@zhouxw modify 2025-06-27 修改监管对象，终端不再同步更改组织
		/*if(!trainCargoBoxInDB.getDeptId().equals(box.getDeptId())){
			// 对于RnssDevice的更新
			rnssDeviceService.updateDept(box.getId(),box.getTargetType(),box.getDeptId());

			// 对于BdmAbstractDevice的更新
			abstractDeviceService.updateDept(box.getId(),box.getTargetType(),box.getDeptId());
		}*/

		return box;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<TrainCargoBoxRequest> importExcel(List<TrainCargoBoxRequest> list) {
		//重复的的数据
		List<TrainCargoBoxRequest> duplicates = getDuplicateRequests(list);
		List<TrainCargoBoxRequest> requests = new ArrayList<>(list);
		// 去重后的数据（去除所有重复数据）
		requests.removeAll(duplicates);

		if (!requests.isEmpty()) {
			List<String> numberList = requests.stream()
					.map(TrainCargoBoxRequest::getNumber)
					.collect(Collectors.toList());
			//数据库的数据
			QueryWrapper<BdmTrainCargoBox> queryWrapper = new QueryWrapper<>();
			if (!numberList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(queryWrapper, "number", numberList);
			}
			queryWrapper.eq("deleted",0);
			queryWrapper.select("number");
			List<String> numberExitList = this.bdmTrainCargoBoxMapper.selectList(queryWrapper)
					.stream()
					.map(BdmTrainCargoBox::getNumber)
					.collect(Collectors.toList());

			List<BdmTrainCargoBox> trainCargoBoxes = new ArrayList<>();
			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.BOX_TARGET_TYPE, BaseInfoConstants.ZERO,BaseInfoConstants.ZERO);
			List<BdmAbstractTarget> bdmAbstractTargets = new ArrayList<>();
			Map<String, String> map = new HashMap<>();

			for (TrainCargoBoxRequest request : requests) {
				if(numberExitList.contains(request.getNumber())){
					request.setMsg("货车车厢编号已存在");
					duplicates.add(request);
				}else {
					BdmTrainCargoBox trainCargoBox = new BdmTrainCargoBox();
					trainCargoBox.setId(targetId.nextId());
					trainCargoBox.setNumber(request.getNumber() != null ? request.getNumber() : "");
					trainCargoBox.setTargetType(TargetTypeEnum.BOX.getSymbol());
					trainCargoBox.setModel(request.getModel() != null ? request.getModel() : 0);
					trainCargoBox.setSize(request.getSize() != null ? request.getSize() : 0);
					trainCargoBox.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
					trainCargoBox.setMaxGross(request.getMaxGross() != null ? request.getMaxGross() : 0);
					trainCargoBox.setTare(request.getTare() != null ? request.getTare() : 0);
					trainCargoBox.setNet(request.getNet() != null ? request.getNet() : 0);
					trainCargoBox.setCuCap(request.getCuCap() != null ? request.getCuCap() : 0);
					trainCargoBox.setLength(request.getLength() != null ? request.getLength() : 0);
					trainCargoBox.setHeight(request.getHeight() != null ? request.getHeight() : 0);
					trainCargoBox.setCreateTime(new Date());
					trainCargoBox.setDeleted(0);
					trainCargoBoxes.add(trainCargoBox);
					request.setId(trainCargoBox.getId());

					String key = trainCargoBox.getTargetType() + "-" + trainCargoBox.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", trainCargoBox.getNumber());
					innerMap.put("targetType", trainCargoBox.getTargetType());
					innerMap.put("deptId", trainCargoBox.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}

					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(trainCargoBox, abstractTarget);
					abstractTarget.setName(trainCargoBox.getNumber());
					abstractTarget.setCategory(0);
					bdmAbstractTargets.add(abstractTarget);
				}
			}

			if (!trainCargoBoxes.isEmpty()) {
				//将剩余的数据批量插入数据库
				MapperUtils.splitListByCapacity(trainCargoBoxes, MapperUtils.DEFAULT_CAPACITY)
					.forEach(boxList -> this.bdmTrainCargoBoxMapper.insertBatch(boxList));

				MapperUtils.splitListByCapacity(bdmAbstractTargets, MapperUtils.DEFAULT_CAPACITY)
					.forEach(abstractTargetList -> this.abstractTargetService.insertBatch(abstractTargetList));

				if(!map.isEmpty()){
					redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);
				}

				BdmTrainCargoBox last = trainCargoBoxes.get(trainCargoBoxes.size() - 1);
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setTargetId(last.getId());
				deviceInfo.setTargetType(last.getTargetType());
				deviceInfo.setTargetName(last.getNumber());
				deviceInfo.setDeptId(last.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_train_cargo_box", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("车辆信息更新消息发送到kafka失败", e);
				}
			}
		}
		return duplicates;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest) {
		int update = bdmTrainCargoBoxMapper.batchUpdate(batchUpdateRequest.getIds(),batchUpdateRequest.getDeptId());
		if (update == 0) {
			return false;
		}
		//查询出所有的本次要更新的字段
		List<BdmTrainCargoBox> bdmTrainCargoBoxList = bdmTrainCargoBoxMapper
			.selectList(new LambdaQueryWrapper<BdmTrainCargoBox>().in(BdmTrainCargoBox::getId, batchUpdateRequest.getIds()));
		//转换
		List<BdmAbstractTarget> bdmAbstractTargetList = bdmTrainCargoBoxList.stream().map(BdmAbstractTargetConverter::toBdmTrainCargoBox).collect(Collectors.toList());
		//更新主表
		abstractTargetService.saveOrUpdateBatch(bdmAbstractTargetList);
		//批量更新redis和kafka
		batchProcess(bdmTrainCargoBoxList);
		if (update > 0){
			return true;
		}
		return false;
	}

	/**
	 * 批量更新redis
	 * 批量发送kafka
	 */
	private void batchProcess(List<BdmTrainCargoBox> bdmTrainCargoBoxList){
		for (BdmTrainCargoBox box : bdmTrainCargoBoxList) {
			Map<String, String> map = new HashMap<>();
			String key = box.getTargetType() + "-" + box.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("targetName", box.getNumber());
			innerMap.put("targetType", box.getTargetType());
			innerMap.put("deptId", box.getDeptId());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：Map<String, String> map这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setTargetId(box.getId());
			deviceInfo.setTargetName(box.getNumber());
			deviceInfo.setDeptId(box.getDeptId());
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_train_cargo_box", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("铁路货车车厢信息更新消息发送到kafka失败", e);
			}
		}
	}

	public List<TrainCargoBoxRequest> getDuplicateRequests(List<TrainCargoBoxRequest> list) {
		Map<String, Long> numberCountMap = list.stream()
			.map(TrainCargoBoxRequest::getNumber)
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

		List<String> duplicateNumbers = numberCountMap.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		return list.stream()
			.filter(bdmTrainCargoBox -> duplicateNumbers.contains(bdmTrainCargoBox.getNumber()))
			.peek(bdmTrainCargoBox -> bdmTrainCargoBox.setMsg("货车车厢编号重复"))
			.collect(Collectors.toList());
	}
}
