--- get_dept_tree_with_stats.lua
-- 获取部门树结构（只包含部门节点和统计数据）
-- V2: 该版本不再需要任何KEY，始终返回完整的部门树。
--
-- KEYS: (No keys are used)

-- 1. 获取当前活跃版本
local currentVersionKey = "tree:current_version"
local currentVersion = redis.call('GET', currentVersionKey)
if not currentVersion then
    currentVersion = "v1" -- 默认版本
end

-- 2. 使用版本化的key前缀
local nodeKeyPrefix = "tree:" .. currentVersion .. ":node:"
local childrenKeyPrefix = "tree:" .. currentVersion .. ":children:"

local virtualRootId = "dept_0" -- Hardcoded virtual root ID

-- 递归获取部门树的函数
local function getDeptTree(nodeId, visited)
    if visited[nodeId] then
        return nil -- 防止循环引用
    end
    visited[nodeId] = true

    local nodeKey = nodeKeyPrefix .. nodeId

    -- 检查节点是否存在
    if redis.call('EXISTS', nodeKey) == 0 then
        return nil
    end

    -- 获取节点数据
    local nodeData = redis.call('HGETALL', nodeKey)
    if not nodeData or #nodeData == 0 then
        return nil
    end

    -- 将数组转换为哈希表
    local node = {}
    for i = 1, #nodeData, 2 do
        node[nodeData[i]] = nodeData[i + 1]
    end

    -- 只处理部门类型的节点
    if node.type ~= 'dept' then
        return nil
    end

    -- 优化：先构建子节点列表
    local childrenKey = childrenKeyPrefix .. nodeId
    local childrenIds = redis.call('ZRANGE', childrenKey, 0, -1)
    local childrenNodes = {} -- 单独创建子节点表

    for _, childId in ipairs(childrenIds) do
        -- 只处理部门类型的子节点
        local childNodeKey = nodeKeyPrefix .. childId
        local childType = redis.call('HGET', childNodeKey, 'type')

        if childType == 'dept' then
            local childNode = getDeptTree(childId, visited)
            if childNode then
                table.insert(childrenNodes, childNode)
            end
        end
    end

    -- 构建返回的节点对象
    local result = {
        id = node.id,
        deptId = node.deptId,
        name = node.name,
        type = node.type,
        parentId = node.parentId,
        total = tonumber(node.total) or 0,
        onlineNum = tonumber(node.onlineNum) or 0,
        selfTotal = tonumber(node.selfTotal) or 0,
        selfOnlineNum = tonumber(node.selfOnlineNum) or 0
        -- children字段在此处暂时不赋值
    }

    -- 优化：只有当子节点列表不为空时，才将children字段加入结果中
    if #childrenNodes > 0 then
        result.children = childrenNodes
    end

    return result
end

-- 主逻辑: 始终从虚拟根节点开始构建完整的树
local visited = {}
local finalResult = {} -- 总是创建一个结果表

-- 直接从虚拟根的子节点开始
local childrenKey = childrenKeyPrefix .. virtualRootId
local childrenIds = redis.call('ZRANGE', childrenKey, 0, -1)

for _, childId in ipairs(childrenIds) do
    local childNodeKey = nodeKeyPrefix .. childId
    local childType = redis.call('HGET', childNodeKey, 'type')

    if childType == 'dept' then
        local childNode = getDeptTree(childId, visited)
        if childNode then
            table.insert(finalResult, childNode)
        end
    end
end

-- 最终修正：检查finalResult表是否为空。
if #finalResult > 0 then
    return cjson.encode(finalResult)
else
    return "[]"
end

