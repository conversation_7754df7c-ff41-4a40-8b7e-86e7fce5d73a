--- update_device_acc_status.lua

-- KEYS[1]: deviceId
-- ARGV[1]: newAccStatus
--
-- Updates a device's ACC status.
--
-- Returns:
-- 1 if the status was updated.
-- 0 if device not found.

local deviceId = KEYS[1]
local newStatus = ARGV[1]
local nodeKeyPrefix = "tree:node:"

local deviceKey = nodeKeyPrefix .. deviceId

if redis.call('EXISTS', deviceKey) == 0 then
    return 0
end

redis.call('HSET', deviceKey, 'acc', newStatus)

return 1
