package org.springblade.websocket.cache;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Redis缓存树操作核心管理器
 */
@Component
public class TreeCacheManager {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;

	private static final String BUILD_TREE_LOCK_KEY = "tree:lock:build";
	private static final long LOCK_TIMEOUT_MINUTES = 10;

	/**
	 * 获取构建树的分布式锁
	 * @return true 如果获取锁成功
	 */
	public boolean acquireBuildLock() {
		return Boolean.TRUE.equals(
			redisTemplate.opsForValue().setIfAbsent(BUILD_TREE_LOCK_KEY, "building", LOCK_TIMEOUT_MINUTES, TimeUnit.MINUTES)
		);
	}

	/**
	 * 释放构建树的分布式锁
	 */
	public void releaseBuildLock() {
		redisTemplate.delete(BUILD_TREE_LOCK_KEY);
	}

	/**
	 * 检查是否有构建任务正在进行
	 * @return true-有构建任务正在进行，false-没有构建任务
	 */
	public boolean isBuildInProgress() {
		return Boolean.TRUE.equals(redisTemplate.hasKey(BUILD_TREE_LOCK_KEY));
	}

	/**
	 * 等待构建任务完成
	 * @param timeoutSeconds 超时时间（秒）
	 * @return true-构建任务完成，false-超时或出错
	 */
	public boolean waitForBuildCompletion(long timeoutSeconds) {
		long startTime = System.currentTimeMillis();
		long timeoutMillis = timeoutSeconds * 1000;

		while (isBuildInProgress()) {
			try {
				// 检查是否超时
				if (timeoutMillis > 0 && (System.currentTimeMillis() - startTime) > timeoutMillis) {
					return false;
				}

				// 等待一小段时间后再检查
				Thread.sleep(100);
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
				return false;
			}
		}

		return true;
	}

}
