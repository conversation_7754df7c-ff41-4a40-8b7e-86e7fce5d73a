package org.springblade.websocket.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.websocket.dto.tree.BaseNode;
import org.springblade.websocket.dto.tree.TreeExpandRequest;
import org.springblade.websocket.service.TreeService;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/tree")
@AllArgsConstructor
@Slf4j
@Api(value = "部门终端树缓存", tags = "部门终端树缓存接口")
public class TreeController {

	private final TreeService treeService;

	@GetMapping
	public R<List<BaseNode>> getTree(@RequestParam Long rootId) {
		return R.data(treeService.getTree(rootId));
	}

	@GetMapping("/dept-tree")
	public R<List<BaseNode>> getDeptTreeWithStats() {
		return R.data(treeService.getDeptTreeWithStats());
	}

	@GetMapping("/dept-tree-expanded")
	@ApiOperation(value = "获取部门树结构并展开指定节点", notes = "返回全量部门树，对于expandedNodeIds中的节点，还会返回其全部子节点")
	public R<List<BaseNode>> getDeptTreeWithExpandedNodes(
		@ApiParam(value = "需要展开的节点ID列表", required = false) @RequestParam(required = false) List<String> expandedNodeIds) {
		return R.data(treeService.getDeptTreeWithExpandedNodes(expandedNodeIds));
	}


	@GetMapping("/children")
	public R<List<BaseNode>> getChildren(@RequestParam String parentKey) {
		return R.data(treeService.getChildren(parentKey));
	}

	@GetMapping("/search")
	public R<Page<BaseNode>> searchNodes(
		@RequestParam String keyword,
		@RequestParam(required = false) String type,
		@RequestParam(defaultValue = "1") long current,
		@RequestParam(defaultValue = "10") long pageSize,
		BladeUser user
	) {

		return R.data(treeService.searchNodes(keyword, type, current, pageSize));
	}

	@PostMapping("/permitted")
	public R<List<BaseNode>> getPermittedTree(@ApiParam(value = "部门ID列表", required = true) @RequestBody List<Long> deptIds) {
		return R.data(treeService.getPermittedTree(deptIds));
	}

	@GetMapping("/by-creator")
	public R<List<BaseNode>> getTreeByCreator(
		@ApiParam(value = "用户ID", required = true) @RequestParam String userId) {
		return R.data(treeService.getTreeByCreator(userId));
	}

	@PostMapping("/management/warm-up")
	public R<Void> warmUpCache() {
		treeService.buildTree();
		return R.success("缓存预热任务已启动");
	}

	@PostMapping("/management/rebuild")
	public R<Void> rebuildCache() {
		treeService.clearCache();
		treeService.buildTree();
		return R.success("缓存重建任务已完成");
	}

	@PostMapping("/management/clear")
	public R<Void> clearCache() {
		treeService.clearCache();
		return R.success("缓存清除任务已完成");
	}

	@GetMapping("/management/status")
	public R<Map<String, Object>> getCacheStatus() {
		Map<String, Object> status = new HashMap<>();
		status.put("valid", treeService.isCacheValid());
		status.put("version", treeService.getCacheVersion());
		return R.data(status);
	}

	/**
	 * 动态获取树结构（基于展开节点）
	 */
	@PostMapping("/dynamic")
	@ApiOperation("根据展开节点动态获取树结构")
	public R<List<BaseNode>> getDynamicTree(
		@RequestBody TreeExpandRequest request,
		BladeUser user) {

		// 参数验证
		if (request == null || request.getExpandedNodeIds() == null || request.getExpandedNodeIds().isEmpty()) {
			return R.fail("展开节点ID列表不能为空");
		}


		try {
			List<BaseNode> treeData = treeService.getDynamicTree(request.getExpandedNodeIds());
			return R.data(treeData);
		} catch (Exception e) {
			log.error("动态获取树结构失败", e);
			return R.fail("获取树结构失败：" + e.getMessage());
		}
	}

	/**
	 * 检查节点是否有子节点
	 */
	@GetMapping("/has-children")
	@ApiOperation("检查节点是否有子节点")
	public R<Boolean> hasChildren(@RequestParam String nodeId) {
		if (nodeId == null || nodeId.trim().isEmpty()) {
			return R.fail("节点ID不能为空");
		}

		try {
			boolean hasChildren = treeService.hasChildren(nodeId);
			return R.data(hasChildren);
		} catch (Exception e) {
			log.error("检查节点子节点失败: {}", nodeId, e);
			return R.fail("检查节点子节点失败：" + e.getMessage());
		}
	}
}
