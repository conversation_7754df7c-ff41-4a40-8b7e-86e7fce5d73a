package org.springblade.websocket.consumer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springblade.common.constant.DeviceStatusConstant;
import org.springblade.common.enums.Operation;
import org.springblade.entity.Location;
import org.springblade.websocket.cache.LocalDeviceStatusCacheManager;
import org.springblade.websocket.dto.DeviceInfo;
import org.springblade.websocket.dto.DeviceOnOff;
import org.springblade.websocket.dto.DeviceStatusInfo;
import org.springblade.websocket.service.TreeService;
import org.springblade.websocket.service.TreeWebSocketService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 终端部门树增量数据Kafka消费者
 * 处理终端变化、状态变化和位置数据消息
 */
@Slf4j
@Component
public class TreeKafkaConsumer {

	@Resource
	private TreeService treeService;

	@Resource
	private TreeWebSocketService treeWebSocketService;

	@Resource
	private LocalDeviceStatusCacheManager localCacheManager;

	@Value("${acc-show.models}")
	private Set<String> models;

	/**
	 * 位置数据时间过滤阈值（秒）- 3分钟
	 */
	private static final long LOCATION_TIME_FILTER_THRESHOLD_SECONDS = 180L;

	/**
	 * 消费终端、监控对象数据变化消息
	 * 主题：device_target_change_topic
	 * 处理：新增、编辑、删除操作
	 */
	@KafkaListener(
		topics = "device_target_change_topic",
		containerFactory = "treeKafkaListenerContainerFactory",
		batch = "true"
	)
	public void consumeDeviceTargetChange(List<ConsumerRecord<String, String>> records,
										  Acknowledgment acknowledgment) {
		try {
			if (CollectionUtils.isEmpty(records)) {
				log.warn("接收到空的终端、监控对象变化消息列表");
				return;
			}

			log.info("开始处理终端、监控对象变化消息，数量: {}", records.size());

			for (ConsumerRecord<String, String> record : records) {
				try {
					String message = record.value();
					DeviceInfo deviceInfo = JSON.parseObject(message, DeviceInfo.class);

					// 推断操作类型（如果没有明确指定）
					Operation operation = deviceInfo.getOperation();

					log.info("处理终端、监控对象变化事件: deviceId={}, targetId={}, operation={}",
						deviceInfo.getDeviceId(), deviceInfo.getTargetId(), operation);

					switch (operation) {
						case INLET:
							handleAdd(deviceInfo);
							break;
						case UPDATE:
							handleUpdate(deviceInfo);
							break;
						case DELETE:
							handleDelete(deviceInfo);
							break;
						case BIND:
							// handleDeviceBind(deviceInfo);
							break;
						case UNBIND:
							// handleDeviceUnbind(deviceInfo);
							break;
						default:
							log.warn("未知的操作类型: {}", operation);
					}
				} catch (Exception e) {
					log.error("处理单条终端、监控对象变化消息失败: {}", record.value(), e);
				}
			}

			// 手动提交offset
			acknowledgment.acknowledge();
			log.info("终端、监控对象变化消息处理完成");

		} catch (Exception e) {
			log.error("处理终端、监控对象变化消息批次失败", e);
			// 不提交offset，让消息重新消费
		}
	}

	private void handleDeviceUnbind(DeviceInfo deviceInfo) {
		try {
			log.info("处理终端解绑: deviceId={}, targetId={}", deviceInfo.getDeviceId(), deviceInfo.getTargetId());

			// 终端解绑相当于删除操作，重新构建树以确保数据一致性
			treeService.buildTree();

			log.info("终端解绑处理完成，已重新构建树缓存");
		} catch (Exception e) {
			log.error("处理终端解绑失败: deviceInfo={}", deviceInfo, e);
		}
	}

	private void handleDeviceBind(DeviceInfo deviceInfo) {
		try {
			log.info("处理终端绑定: deviceId={}, targetId={}", deviceInfo.getDeviceId(), deviceInfo.getTargetId());

			// 终端绑定相当于新增操作，重新构建树以确保数据一致性
			treeService.buildTree();

			log.info("终端绑定处理完成，已重新构建树缓存");
		} catch (Exception e) {
			log.error("处理终端绑定失败: deviceInfo={}", deviceInfo, e);
		}
	}

	/**
	 * 消费终端状态变化消息
	 * 主题：terminalstatus
	 * 处理：上线、下线状态变化
	 */
	@KafkaListener(
		topics = "terminalstatus",
		containerFactory = "treeKafkaListenerContainerFactory",
		batch = "true"
	)
	public void consumeTerminalStatus(List<ConsumerRecord<String, String>> records,
									  Acknowledgment acknowledgment) {
		try {
			if (CollectionUtils.isEmpty(records)) {
				log.warn("接收到空的终端状态消息列表");
				return;
			}

			log.info("开始处理终端状态消息，数量: {}", records.size());

			for (ConsumerRecord<String, String> record : records) {
				try {
					String message = record.value();
					DeviceOnOff deviceOnOff = JSON.parseObject(message, DeviceOnOff.class);

					log.info("处理终端状态事件: targetId={}, deviceId={}, deviceOnOff={}",
						deviceOnOff.getTargetId(), deviceOnOff.getDeviceId(), deviceOnOff.getOnOffLine());

					// 获取当前上线状态信息
					DeviceStatusInfo currentStatusInfo = treeService.getCurrentDeviceStatusInfo(
						deviceOnOff.getTargetId(), deviceOnOff.getDeviceId(), DeviceStatusInfo.FIELD_ONLINE
					);

					if (currentStatusInfo != null) {
						Integer currentStatus = currentStatusInfo.getStatus();
						Integer newValue = Integer.valueOf(deviceOnOff.getOnOffLine());

						// 检查上线状态是否变化
						if (!Objects.equals(currentStatus, newValue)) {
							// 更新上线状态
							boolean success = updateDeviceState(
								deviceOnOff.getTargetId(),
								deviceOnOff.getDeviceId(),
								DeviceStatusInfo.FIELD_ONLINE,
									newValue,
								currentStatusInfo,
								"上线状态"
							);

							if (success) {
								log.info("终端上线状态更新成功: deviceId={}, {} -> {}, source={}",
									deviceOnOff.getDeviceId(), currentStatus, deviceOnOff.getOnOffLine(),
									currentStatusInfo.getSource());

								// 推送到前端
								treeWebSocketService.pushTerminalStatusChange(deviceOnOff);
							} else {
								log.warn("终端上线状态更新失败: deviceId={}", deviceOnOff.getDeviceId());
							}
						} else {
							log.debug("终端上线状态未发生变化，跳过更新: deviceId={}, action={}",
								deviceOnOff.getDeviceId(), deviceOnOff.getOnOffLine());
						}
					}

				} catch (Exception e) {
					log.error("处理单条终端状态消息失败: {}", record.value(), e);
				}
			}

			// 手动提交offset
			acknowledgment.acknowledge();
			log.info("终端状态消息处理完成");

		} catch (Exception e) {
			log.error("处理终端状态消息批次失败", e);
			// 不提交offset，让消息重新消费
		}
	}

	/**
	 * 消费位置数据消息
	 * 主题：ce.comms.fct.location.0
	 * 处理：ACC状态变化和运动状态变化
	 */
	@KafkaListener(topics = "ce.comms.fct.location.0",
		containerFactory = "treeKafkaListenerContainerFactory",
		batch = "true")
	public void consumeLocationData(List<ConsumerRecord<String, String>> records,
									Acknowledgment acknowledgment) {
		try {
			if (CollectionUtils.isEmpty(records)) {
				log.warn("接收到空的位置数据消息列表");
				return;
			}

			for (ConsumerRecord<String, String> record : records) {
				try {
					String message = record.value();
					Location locationEvent = JSON.parseObject(message, Location.class);

					// 1. 时间过滤：检查定位时间是否超过3分钟
					if (!isLocationTimeValid(locationEvent)) {
						log.debug("位置数据时间过期，跳过处理: deviceId={}, locTime={}, currentTime={}",
								locationEvent.getDeviceId(), locationEvent.getTime(), System.currentTimeMillis() / 1000);
						continue;
					}

					// 2. 处理运动状态变化（集成本地缓存优化）
					processMotionStateChangeOptimized(locationEvent);

					// 3. 处理ACC状态变化（仅限特定终端型号，集成本地缓存优化）
					if (models.contains(locationEvent.getDeviceModel())) {
						processAccStatusChangeOptimized(locationEvent);
					}

				} catch (Exception e) {
					log.error("处理单条位置数据消息失败: {}", record.value(), e);
				}
			}

			// 手动提交offset
			acknowledgment.acknowledge();

		} catch (Exception e) {
			log.error("处理位置数据消息批次失败", e);
			// 不提交offset，让消息重新消费
		}
	}


	/**
	 * 处理终端新增
	 */
	private void handleAdd(DeviceInfo deviceInfo) {
		try {
			String deviceId = getDeviceIdFromInfo(deviceInfo);
			log.info("处理终端新增: deviceId={}, targetId={}", deviceId, deviceInfo.getTargetId());

			// 使用新的增量更新方法
			boolean success = treeService.handleAdd(deviceInfo);

			if (success) {
				log.info("终端更新处理成功: deviceId={}, targetId={}",
					deviceInfo.getDeviceId(), deviceInfo.getTargetId());
			} else {
				log.warn("终端更新处理失败，回退到重建树: deviceId={}, targetId={}",
					deviceInfo.getDeviceId(), deviceInfo.getTargetId());
				// 如果增量更新失败，回退到重建整个树
				// treeService.buildTree();
			}

		} catch (Exception e) {
			log.error("处理终端新增失败: deviceInfo={}", deviceInfo, e);
		}
	}

	/**
	 * 处理终端更新
	 */
	private void handleUpdate(DeviceInfo deviceInfo) {
		try {
			log.info("处理终端、监控对象更新: deviceId={}, targetId={}",
					deviceInfo.getDeviceId(), deviceInfo.getTargetId());

			// 使用新的增量更新方法
			boolean success = treeService.handleUpdate(deviceInfo);

			if (success) {
				log.info("终端、监控对象更新处理成功: deviceId={}, targetId={}",
						deviceInfo.getDeviceId(), deviceInfo.getTargetId());
			} else {
				log.warn("终端、监控对象更新处理失败，回退到重建树: deviceId={}, targetId={}",
						deviceInfo.getDeviceId(), deviceInfo.getTargetId());
				// 如果增量更新失败，回退到重建整个树
				// treeService.buildTree();
			}

		} catch (Exception e) {
			log.error("处理终端、监控对象更新失败: deviceInfo={}", deviceInfo, e);
			// 异常情况下也回退到重建树
			// try {
			// 	treeService.buildTree();
			// } catch (Exception buildException) {
			// 	log.error("重建树也失败了", buildException);
			// }
		}
	}

	/**
	 * 处理终端删除
	 */
	private void handleDelete(DeviceInfo deviceInfo) {
		try {
			String deviceId = getDeviceIdFromInfo(deviceInfo);
			log.info("处理终端删除: deviceId={}, targetId={}", deviceId, deviceInfo.getTargetId());

			boolean success = treeService.handleDelete(deviceInfo);

			if (success) {
				log.info("终端、监控对象删除处理成功: deviceId={}, targetId={}",
					deviceInfo.getDeviceId(), deviceInfo.getTargetId());
			} else {
				log.warn("终端、监控对象删除处理失败，回退到重建树: deviceId={}, targetId={}",
					deviceInfo.getDeviceId(), deviceInfo.getTargetId());
				// 如果增量删除失败，回退到重建整个树
				// treeService.buildTree();
			}


			log.info("终端删除处理完成，已重新构建树缓存");

		} catch (Exception e) {
			log.error("处理终端删除失败: deviceInfo={}", deviceInfo, e);
		}
	}

	/**
	 * 从DeviceInfo中获取终端ID
	 */
	private String getDeviceIdFromInfo(DeviceInfo deviceInfo) {
		if (deviceInfo.getDeviceId() != null) {
			return deviceInfo.getDeviceId().toString();
		}
		if (deviceInfo.getTargetId() != null) {
			return deviceInfo.getTargetId().toString();
		}
		return null;
	}

	/**
	 * 通用的终端状态更新方法
	 * @param targetId 监控对象ID
	 * @param deviceId 终端ID
	 * @param fieldName 字段名称
	 * @param newValue 新值
	 * @param currentStatusInfo 当前状态信息
	 * @param fieldDescription 字段描述（用于日志）
	 * @return 更新是否成功
	 */
	private boolean updateDeviceState(Long targetId, Long deviceId, String fieldName,
									Integer newValue, DeviceStatusInfo currentStatusInfo,
									String fieldDescription) {
		try {
			boolean success = false;

			if (DeviceStatusInfo.FIELD_ONLINE.equals(fieldName)) {
				// 在线状态更新
				success = treeService.updateDeviceStatusBySource(targetId, deviceId, newValue, currentStatusInfo);
			} else if (DeviceStatusInfo.FIELD_FUSION_STATE.equals(fieldName)) {
				// 运动状态更新
				success = treeService.updateDeviceFusionStateBySource(targetId, deviceId, newValue, currentStatusInfo);
			} else if (DeviceStatusInfo.FIELD_ACC.equals(fieldName)) {
				// ACC状态更新
				success = treeService.updateDeviceAccStatusBySource(targetId, deviceId, newValue, currentStatusInfo);
			}

			return success;
		} catch (Exception e) {
			log.error("更新终端{}失败: deviceId={}, {}={}", fieldDescription, deviceId, fieldName, newValue, e);
			return false;
		}
	}

	/**
	 * 检查位置数据时间是否有效
	 * @param locationEvent 位置事件
	 * @return true-时间有效，false-时间过期
	 */
	private boolean isLocationTimeValid(Location locationEvent) {
		if (locationEvent == null || locationEvent.getTime() == null) {
			log.warn("位置数据或定位时间为空，跳过时间验证");
			return false;
		}

		long locationTimeSeconds = locationEvent.getTime();
		long currentTimeSeconds = System.currentTimeMillis() / 1000;
		long timeDifferenceSeconds = currentTimeSeconds - locationTimeSeconds;

		// 如果时间差超过阈值，认为数据过期
		if (timeDifferenceSeconds > LOCATION_TIME_FILTER_THRESHOLD_SECONDS) {
			log.info("位置数据时间过期: deviceId={}, 定位时间={}, 当前时间={}, 时间差={}秒",
					locationEvent.getDeviceId(), locationTimeSeconds, currentTimeSeconds, timeDifferenceSeconds);
			return false;
		}

		return true;
	}

	/**
	 * 处理运动状态变化（优化版本，集成本地缓存）
	 * @param locationEvent 位置事件
	 */
	private void processMotionStateChangeOptimized(Location locationEvent) {
		try {
			Double speed = locationEvent.getSpeed();
			// 根据速度计算运动状态：速度>0为运动(1)，否则为静止(0)
			Integer newFusionState = (speed != null && speed > 0) ? DeviceStatusConstant.FUSION_STATE_MOVE : DeviceStatusConstant.FUSION_STATE_STOP;

			// 1. 首先检查本地缓存中的状态是否有变化
			if (!localCacheManager.isFusionStateChanged(locationEvent.getDeviceId(), newFusionState)) {
				log.debug("本地缓存显示运动状态未变化，跳过处理: deviceId={}, fusionState={}",
						locationEvent.getDeviceId(), newFusionState);
				return;
			}

			// 2. 本地缓存显示状态有变化，进一步检查Redis中的状态
			DeviceStatusInfo currentFusionInfo = treeService.getCurrentDeviceStatusInfo(
					locationEvent.getTargetId(), locationEvent.getDeviceId(), DeviceStatusInfo.FIELD_FUSION_STATE);

			if (currentFusionInfo != null) {
				Integer currentFusionState = currentFusionInfo.getFusionState();

				// 3. 检查Redis中的运动状态是否真的有变化
				if (!Objects.equals(currentFusionState, newFusionState)) {
					log.info("处理运动状态事件: targetId={}, deviceId={}, speed={}, fusionState={} -> {}",
							locationEvent.getTargetId(), locationEvent.getDeviceId(),
							locationEvent.getSpeed(), currentFusionState, newFusionState);

					// 4. 更新运动状态
					boolean success = updateDeviceState(
							locationEvent.getTargetId(),
							locationEvent.getDeviceId(),
							DeviceStatusInfo.FIELD_FUSION_STATE,
							newFusionState,
							currentFusionInfo,
							"运动状态"
					);

					if (success) {
						log.info("终端运动状态更新成功: deviceId={}, {} -> {}, source={}",
								locationEvent.getDeviceId(), currentFusionState, newFusionState,
								currentFusionInfo.getSource());

						// 5. 更新本地缓存
						localCacheManager.updateDeviceStatusCache(
								locationEvent.getDeviceId(), newFusionState, null, locationEvent.getDeviceModel());

						// 6. 推送运动状态变化到前端
						treeWebSocketService.pushDeviceChange(locationEvent.getDeviceId(), "MOTION_STATE_CHANGE");
					} else {
						log.warn("终端运动状态更新失败: deviceId={}", locationEvent.getDeviceId());
					}
				} else {
					log.debug("Redis中运动状态未发生变化，更新本地缓存: deviceId={}, fusionState={}",
							locationEvent.getDeviceId(), newFusionState);
					// 更新本地缓存以保持一致性
					localCacheManager.updateDeviceStatusCache(
							locationEvent.getDeviceId(), newFusionState, null, locationEvent.getDeviceModel());
				}
			}
		} catch (Exception e) {
			log.error("处理运动状态变化失败: deviceId={}", locationEvent.getDeviceId(), e);
		}
	}


	/**
	 * 处理ACC状态变化（优化版本，集成本地缓存）
	 * @param location 位置事件
	 */
	private void processAccStatusChangeOptimized(Location location) {
		try {
			Integer stateFlag = location.getStatus();
			if (stateFlag != null) {
				Integer accStatus = stateFlag & 1;

				// 1. 首先检查本地缓存中的ACC状态是否有变化
				if (!localCacheManager.isAccStatusChanged(location.getDeviceId(), accStatus)) {
					log.debug("本地缓存显示ACC状态未变化，跳过处理: deviceId={}, accStatus={}",
							location.getDeviceId(), accStatus);
					return;
				}

				// 2. 本地缓存显示状态有变化，进一步检查Redis中的状态
				DeviceStatusInfo currentAccInfo = treeService.getCurrentDeviceStatusInfo(
						location.getTargetId(), location.getDeviceId(), DeviceStatusInfo.FIELD_ACC);

				if (currentAccInfo != null) {
					Integer currentAccStatus = currentAccInfo.getAccStatus();

					// 3. 检查Redis中的ACC状态是否真的有变化
					if (!Objects.equals(currentAccStatus, accStatus)) {
						log.info("处理ACC状态事件: targetId={}, deviceId={}, accStatus={} -> {}",
								location.getTargetId(), location.getDeviceId(),
								currentAccStatus, accStatus);

						// 4. 更新ACC状态
						boolean success = updateDeviceState(
								location.getTargetId(),
								location.getDeviceId(),
								DeviceStatusInfo.FIELD_ACC,
								accStatus,
								currentAccInfo,
								"ACC状态"
						);

						if (success) {
							log.info("终端ACC状态更新成功: deviceId={}, {} -> {}, source={}",
									location.getDeviceId(), currentAccStatus, accStatus,
									currentAccInfo.getSource());

							// 5. 更新本地缓存
							localCacheManager.updateDeviceStatusCache(
									location.getDeviceId(), null, accStatus, location.getDeviceModel());

							// 6. 推送ACC状态变化到前端
							treeWebSocketService.pushAccStatusChange(location);
						} else {
							log.warn("终端ACC状态更新失败: deviceId={}", location.getDeviceId());
						}
					} else {
						log.debug("Redis中ACC状态未发生变化，更新本地缓存: deviceId={}, accStatus={}",
								location.getDeviceId(), accStatus);
						// 更新本地缓存以保持一致性
						localCacheManager.updateDeviceStatusCache(
								location.getDeviceId(), null, accStatus, location.getDeviceModel());
					}
				}
			}
		} catch (Exception e) {
			log.error("处理ACC状态变化失败: deviceId={}", location.getDeviceId(), e);
		}
	}

}
