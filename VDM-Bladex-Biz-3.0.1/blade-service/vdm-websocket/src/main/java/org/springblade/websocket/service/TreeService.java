package org.springblade.websocket.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.websocket.dto.DeviceInfo;
import org.springblade.websocket.dto.DeviceStatusInfo;
import org.springblade.websocket.dto.tree.BaseNode;

import java.util.List;

public interface TreeService {

	/**
	 * 构建完整的树缓存
	 */
	void buildTree();

	/**
	 * 从缓存中获取完整的树结构
	 * @param rootId 根节点ID
	 * @return 节点列表
	 */
	List<BaseNode> getTree(Long rootId);


	/**
	 * 获取部门树结构（包含统计数据，不包含设备详情）
	 * 适用于首次加载，保证统计数据完整性的同时减少数据传输量
	 * @return 部门节点列表（包含统计数据）
	 */
	List<BaseNode> getDeptTreeWithStats();

	/**
	 * 获取部门树结构，并展开指定节点的全部子节点
	 * 返回全量部门树，对于expandedNodeIds中的节点，还会返回其全部子节点（包括非部门节点）
	 * @param expandedNodeIds 需要展开的节点ID列表
	 * @return 部门节点列表（包含统计数据和展开的子节点）
	 */
	List<BaseNode> getDeptTreeWithExpandedNodes(List<String> expandedNodeIds);

	/**
	 * [新增] 根据父节点ID懒加载获取其直接子节点列表
	 * @param parentKey 带前缀的父节点Redis Key
	 * @return 子节点列表
	 */
	List<BaseNode> getChildren(String parentKey);

	/**
	 * search device node by keyword
	 * @param keyword keyword
	 * @param type device type
	 * @param current current page
	 * @param pageSize page size
	 * @return Page<BaseNode>
	 */
	Page<BaseNode> searchNodes(String keyword, String type, long current, long pageSize);

	/**
	 * 根据用户权限获取部分树结构
	 * @param deptIds 用户有权限的部门ID列表
	 * @return
	 */
	List<BaseNode> getPermittedTree(List<Long> deptIds);

	/**
	 * 根据创建者ID获取其创建的终端树
	 * @param userId 创建者ID
	 * @return
	 */
	List<BaseNode> getTreeByCreator(String userId);

	/**
	 * 更新设备在线状态
	 * @param deviceId 设备ID
	 * @param onlineStatus 在线状态
	 * @return
	 */
	boolean updateDeviceStatus(Long deviceId, Integer onlineStatus);

	/**
	 * 根据状态来源信息更新设备状态
	 * @param targetId 监控对象ID
	 * @param deviceId 设备ID
	 * @param onlineStatus 在线状态
	 * @param statusInfo 当前状态信息（包含来源）
	 * @return
	 */
	boolean updateDeviceStatusBySource(Long targetId, Long deviceId, Integer onlineStatus, DeviceStatusInfo statusInfo);

	/**
	 * 根据状态来源信息更新设备运动状态
	 * @param targetId 监控对象ID
	 * @param deviceId 设备ID
	 * @param fusionState 运动状态
	 * @param statusInfo 当前状态信息（包含来源）
	 * @return
	 */
	boolean updateDeviceFusionStateBySource(Long targetId, Long deviceId, Integer fusionState, DeviceStatusInfo statusInfo);

	/**
	 * 更新设备ACC状态
	 * @param deviceId 设备ID
	 * @param accStatus ACC状态
	 * @return
	 */
	boolean updateDeviceAccStatus(Long deviceId, Long accStatus);

	/**
	 * 根据状态来源信息更新设备ACC状态
	 * @param targetId 监控对象ID
	 * @param deviceId 设备ID
	 * @param accStatus ACC状态
	 * @param statusInfo 当前状态信息（包含来源）
	 * @return
	 */
	boolean updateDeviceAccStatusBySource(Long targetId, Long deviceId, Integer accStatus, DeviceStatusInfo statusInfo);

	/**
	 * 清除整个树缓存
	 */
	void clearCache();

	/**
	 * 检查缓存是否有效
	 * @return
	 */
	boolean isCacheValid();

	/**
	 * 获取当前缓存版本号
	 * @return
	 */
	long getCacheVersion();

	/**
	 * 根据展开节点列表动态构建树结构
	 * @param expandedNodeIds 展开的节点ID列表
	 * @return 动态构建的树结构
	 */
	List<BaseNode> getDynamicTree(List<String> expandedNodeIds);

	/**
	 * 检查节点是否有子节点
	 * @param nodeId 节点ID
	 * @return 是否有子节点
	 */
	boolean hasChildren(String nodeId);


	/**
	 * 获取设备指定字段的状态信息（包含状态来源）
	 *
	 * @param targetId 监控对象ID
	 * @param deviceId 设备ID
	 * @param fieldNames 需要获取的字段名列表（如 ["online"], ["online", "fusionState"], ["acc"]）
	 * @return 设备状态信息（只包含指定字段和来源）
	 */
	DeviceStatusInfo getCurrentDeviceStatusInfo(Long targetId, Long deviceId, String... fieldNames);

	/**
	 * 刷新设备缓存信息
	 * @param deviceId 设备ID
	 */
	void refreshDeviceCache(String deviceId);

	/**
	 * 获取监控对象下的设备数量
	 * @param targetId 监控对象ID
	 * @return 设备数量
	 */
	long getDeviceCountUnderTarget(Long targetId);

	/**
	 * 获取设备的当前部门信息
	 * @param deviceId 设备ID
	 * @return 当前部门ID，如果设备不存在返回null
	 */
	Long getCurrentDeviceDeptId(Long deviceId);

	/**
	 * 更新设备信息（支持增量更新）
	 * @param deviceInfo 设备信息
	 * @return 更新是否成功
	 */
	boolean updateDeviceInfo(DeviceInfo deviceInfo);

	/**
	 * 更新监控对象名称
	 * @param targetId 监控对象ID
	 * @param newName 新名称
	 * @return 更新是否成功
	 */
	boolean updateTargetName(Long targetId, String newName);
}
