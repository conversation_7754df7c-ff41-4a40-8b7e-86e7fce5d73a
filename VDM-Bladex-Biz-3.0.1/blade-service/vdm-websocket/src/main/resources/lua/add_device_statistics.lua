--- add_device_statistics.lua
-- 新增终端时原子性更新统计数据
--
-- KEYS[1]: targetId (监控对象ID，如 target_123)
-- ARGV[1]: deptId (部门ID)
-- ARGV[2]: deviceType (终端类型)
-- ARGV[3]: isOnline (是否在线: "1"表示在线, "0"表示离线)
-- ARGV[4]: currentVersion (当前版本号，如 v1)
--
-- 原子性地更新新增终端相关的所有统计数据：
-- 1. 监控对象统计
-- 2. 终端类型统计
-- 3. 部门统计（递归向上）
--
-- 返回值:
-- 1 表示统计更新成功
-- 0 表示更新失败

local targetId = KEYS[1]
local deptId = ARGV[1]
local deviceType = ARGV[2]
local isOnline = ARGV[3]
local currentVersion = ARGV[4]

-- 构建版本化的key前缀
local nodeKeyPrefix = "tree:" .. currentVersion .. ":node:"

-- 计算增量
local totalIncrement = 1
local onlineIncrement = (isOnline == "1") and 1 or 0

-- 1. 更新监控对象统计
local targetKey = nodeKeyPrefix .. targetId
if redis.call('EXISTS', targetKey) == 1 then
    redis.call('HINCRBY', targetKey, 'total', totalIncrement)
    redis.call('HINCRBY', targetKey, 'onlineNum', onlineIncrement)
end

-- 2. 更新终端类型统计
local deviceTypeKey = nodeKeyPrefix .. "device_type_" .. deptId .. "_" .. deviceType
if redis.call('EXISTS', deviceTypeKey) == 1 then
    redis.call('HINCRBY', deviceTypeKey, 'total', totalIncrement)
    redis.call('HINCRBY', deviceTypeKey, 'onlineNum', onlineIncrement)
end

-- 3. 递归更新部门统计
local function updateDeptStatistics(currentDeptId)
    if not currentDeptId or currentDeptId == '' or currentDeptId == '0' then
        return
    end
    
    local deptKey = nodeKeyPrefix .. "dept_" .. currentDeptId
    if redis.call('EXISTS', deptKey) == 1 then
        -- 更新部门的selfTotal和selfOnlineNum（直属统计）
        redis.call('HINCRBY', deptKey, 'selfTotal', totalIncrement)
        redis.call('HINCRBY', deptKey, 'selfOnlineNum', onlineIncrement)
        
        -- 更新部门的total和onlineNum（包含子部门统计）
        redis.call('HINCRBY', deptKey, 'total', totalIncrement)
        redis.call('HINCRBY', deptKey, 'onlineNum', onlineIncrement)
        
        -- 获取父部门ID并递归更新
        local parentId = redis.call('HGET', deptKey, 'parentId')
        if parentId and parentId ~= '0' and parentId ~= '' then
            -- 递归更新父部门（但不更新selfTotal和selfOnlineNum）
            updateParentDeptStatistics(parentId)
        end
    end
end

-- 递归更新父部门统计（不包括selfTotal和selfOnlineNum）
local function updateParentDeptStatistics(parentDeptId)
    if not parentDeptId or parentDeptId == '' or parentDeptId == '0' then
        return
    end
    
    local parentDeptKey = nodeKeyPrefix .. "dept_" .. parentDeptId
    if redis.call('EXISTS', parentDeptKey) == 1 then
        -- 只更新total和onlineNum（包含子部门统计）
        redis.call('HINCRBY', parentDeptKey, 'total', totalIncrement)
        redis.call('HINCRBY', parentDeptKey, 'onlineNum', onlineIncrement)
        
        -- 获取上级部门ID并继续递归
        local grandParentId = redis.call('HGET', parentDeptKey, 'parentId')
        if grandParentId and grandParentId ~= '0' and grandParentId ~= '' then
            updateParentDeptStatistics(grandParentId)
        end
    end
end

-- 开始更新部门统计
updateDeptStatistics(deptId)

return 1
