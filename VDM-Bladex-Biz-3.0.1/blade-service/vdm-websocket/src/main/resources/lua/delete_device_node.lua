--- delete_device_node.lua
-- 删除终端节点并原子性更新统计数据
--
-- KEYS[1]: deviceId (终端ID，如 device_123)
-- ARGV[1]: currentVersion (当前版本号，如 v1)
--
-- 原子性地删除终端节点并更新相关统计数据：
-- 1. 判断是单终端模式还是多终端模式
-- 2. 删除相应的节点
-- 3. 更新统计数据
-- 4. 清理空节点
-- 5. 处理多终端转单终端的情况
--
-- 返回值:
-- 1 表示删除成功
-- 0 表示终端不存在
-- -1 表示删除失败

local deviceId = KEYS[1]
local currentVersion = ARGV[1]

-- 构建版本化的key前缀
local nodeKeyPrefix = "tree:" .. currentVersion .. ":node:"
local childrenKeyPrefix = "tree:" .. currentVersion .. ":children:"

-- 1. 检查终端节点是否存在
local deviceKey = nodeKeyPrefix .. deviceId
if redis.call('EXISTS', deviceKey) == 0 then
    return 0
end

-- 2. 获取终端信息
local deviceData = redis.call('HGETALL', deviceKey)
if not deviceData or #deviceData == 0 then
    return 0
end

-- 将数组转换为哈希表
local device = {}
for i = 1, #deviceData, 2 do
    device[deviceData[i]] = deviceData[i + 1]
end

local targetId = device.targetId
local deptId = device.deptId
local deviceType = device.deviceType
local isOnline = tonumber(device.online or "1") == 0

-- 计算统计减量
local totalDecrement = 1
local onlineDecrement = isOnline and 1 or 0

-- 3. 获取监控对象信息
local targetKey = nodeKeyPrefix .. "target_" .. targetId
if redis.call('EXISTS', targetKey) == 0 then
    return -1
end

-- 4. 检查监控对象下的终端数量
local targetChildrenKey = childrenKeyPrefix .. "target_" .. targetId
local childrenCount = redis.call('ZCARD', targetChildrenKey)

-- 5. 判断删除模式
local isSingleDeviceMode = false
local isMultipleDeviceMode = false

-- 检查监控对象节点是否包含终端属性（单终端模式）
local targetDeviceId = redis.call('HGET', targetKey, 'deviceId')
if targetDeviceId and targetDeviceId == device.id then
    isSingleDeviceMode = true
elseif childrenCount > 0 then
    isMultipleDeviceMode = true
end

-- 6. 执行删除逻辑
if isSingleDeviceMode then
    -- 单终端模式：删除整个监控对象节点
    redis.call('DEL', targetKey)
    
    -- 从终端类型的children中移除监控对象
    local deviceTypeKey = "device_type_" .. deptId .. "_" .. deviceType
    local deviceTypeChildrenKey = childrenKeyPrefix .. deviceTypeKey
    redis.call('ZREM', deviceTypeChildrenKey, "target_" .. targetId)
    
elseif isMultipleDeviceMode then
    -- 多终端模式：只删除终端节点
    redis.call('DEL', deviceKey)
    
    -- 从监控对象的children中移除终端
    redis.call('ZREM', targetChildrenKey, deviceId)
    
    -- 检查是否需要转换为单终端模式
    local remainingChildren = redis.call('ZRANGE', targetChildrenKey, 0, -1)
    if #remainingChildren == 1 then
        -- 转换为单终端模式：将剩余终端的属性合并到监控对象节点
        local remainingDeviceKey = nodeKeyPrefix .. remainingChildren[1]
        local remainingDeviceData = redis.call('HGETALL', remainingDeviceKey)
        
        if remainingDeviceData and #remainingDeviceData > 0 then
            -- 将终端属性复制到监控对象节点
            for i = 1, #remainingDeviceData, 2 do
                local field = remainingDeviceData[i]
                local value = remainingDeviceData[i + 1]
                if field == "id" then
                    redis.call('HSET', targetKey, 'deviceId', value)
                elseif field ~= "targetId" and field ~= "parentId" and field ~= "type" then
                    redis.call('HSET', targetKey, field, value)
                end
            end
            
            -- 删除剩余的终端节点
            redis.call('DEL', remainingDeviceKey)
            
            -- 清空监控对象的children
            redis.call('DEL', targetChildrenKey)
        end
    end
else
    -- 未知模式，返回错误
    return -1
end

-- 7. 更新统计数据
-- 更新监控对象统计（如果还存在）
if redis.call('EXISTS', targetKey) == 1 then
    redis.call('HINCRBY', targetKey, 'total', -totalDecrement)
    redis.call('HINCRBY', targetKey, 'onlineNum', -onlineDecrement)
end

-- 更新终端类型统计
local deviceTypeKey = nodeKeyPrefix .. "device_type_" .. deptId .. "_" .. deviceType
if redis.call('EXISTS', deviceTypeKey) == 1 then
    local newTotal = redis.call('HINCRBY', deviceTypeKey, 'total', -totalDecrement)
    redis.call('HINCRBY', deviceTypeKey, 'onlineNum', -onlineDecrement)
    
    -- 如果终端类型节点为空，删除它
    if tonumber(newTotal) == 0 then
        redis.call('DEL', deviceTypeKey)
        
        -- 从部门的children中移除终端类型
        local deptKey = "dept_" .. deptId
        local deptChildrenKey = childrenKeyPrefix .. deptKey
        redis.call('ZREM', deptChildrenKey, "device_type_" .. deptId .. "_" .. deviceType)
    end
end

-- 8. 递归更新部门统计并清理空部门
local function updateDeptStatistics(currentDeptId)
    if not currentDeptId or currentDeptId == '' or currentDeptId == '0' then
        return
    end
    
    local deptKey = nodeKeyPrefix .. "dept_" .. currentDeptId
    if redis.call('EXISTS', deptKey) == 1 then
        -- 减少部门的selfTotal和selfOnlineNum（直属统计）
        redis.call('HINCRBY', deptKey, 'selfTotal', -totalDecrement)
        redis.call('HINCRBY', deptKey, 'selfOnlineNum', -onlineDecrement)
        
        -- 减少部门的total和onlineNum（包含子部门统计）
        local newTotal = redis.call('HINCRBY', deptKey, 'total', -totalDecrement)
        redis.call('HINCRBY', deptKey, 'onlineNum', -onlineDecrement)
        
        -- 如果部门为空且没有子部门，删除部门节点
        if tonumber(newTotal) == 0 then
            local deptChildrenKey = childrenKeyPrefix .. "dept_" .. currentDeptId
            local childrenCount = redis.call('ZCARD', deptChildrenKey)
            
            if childrenCount == 0 then
                -- 获取父部门ID
                local parentId = redis.call('HGET', deptKey, 'parentId')
                
                -- 删除部门节点
                redis.call('DEL', deptKey)
                redis.call('DEL', deptChildrenKey)
                
                -- 从父部门的children中移除当前部门
                if parentId and parentId ~= '0' and parentId ~= '' then
                    local parentChildrenKey = childrenKeyPrefix .. "dept_" .. parentId
                    redis.call('ZREM', parentChildrenKey, "dept_" .. currentDeptId)
                    
                    -- 递归检查父部门
                    updateDeptStatistics(parentId)
                else
                    -- 从根节点移除
                    local rootChildrenKey = childrenKeyPrefix .. "dept_0"
                    redis.call('ZREM', rootChildrenKey, "dept_" .. currentDeptId)
                end
                
                return
            end
        end
        
        -- 获取父部门ID并递归更新（但不删除）
        local parentId = redis.call('HGET', deptKey, 'parentId')
        if parentId and parentId ~= '0' and parentId ~= '' then
            updateParentDeptStatistics(parentId)
        end
    end
end

-- 递归更新父部门统计（不包括selfTotal和selfOnlineNum，也不删除）
local function updateParentDeptStatistics(parentDeptId)
    if not parentDeptId or parentDeptId == '' or parentDeptId == '0' then
        return
    end
    
    local parentDeptKey = nodeKeyPrefix .. "dept_" .. parentDeptId
    if redis.call('EXISTS', parentDeptKey) == 1 then
        -- 只减少total和onlineNum（包含子部门统计）
        redis.call('HINCRBY', parentDeptKey, 'total', -totalDecrement)
        redis.call('HINCRBY', parentDeptKey, 'onlineNum', -onlineDecrement)
        
        -- 获取上级部门ID并继续递归
        local grandParentId = redis.call('HGET', parentDeptKey, 'parentId')
        if grandParentId and grandParentId ~= '0' and grandParentId ~= '' then
            updateParentDeptStatistics(grandParentId)
        end
    end
end

-- 开始更新部门统计
updateDeptStatistics(deptId)

return 1
